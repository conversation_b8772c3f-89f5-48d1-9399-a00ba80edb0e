plugins {
    `java-library`
    id("org.springframework.boot") version "3.4.2"
    id("io.freefair.lombok") version "8.14"
}

group = "com.sast.payment"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

dependencies {
    implementation(platform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))

    implementation("org.springframework:spring-web")
    implementation("jakarta.validation:jakarta.validation-api")
}
