<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEPA Mandate Management</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .loading {
            display: inline-block;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100">
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <h1 class="text-3xl font-bold mb-8 text-center text-blue-800">SEPA Mandate Management</h1>

    <!-- New button to show the create mandate form -->
    <div class="mb-8">
        <button id="showCreateMandateButton" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Create Mandate
        </button>
    </div>

    <!-- Success message -->
    <div id="successMessage" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 hidden flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <span id="successMessageText"></span>
    </div>

    <!-- Error message -->
    <div id="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 hidden flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
        <span id="errorMessageText"></span>
    </div>

    <!-- Create Mandate Form container (initially hidden) -->
    <div id="createMandateContainer" class="hidden">
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Create New SEPA Mandate
        </h2>

        <!-- Step 1: Company ID Input -->
        <div id="companyIdStep" class="mb-6">
            <h3 class="text-lg font-medium mb-3 text-gray-800">Step 1: Enter Company ID</h3>
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-grow">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Company ID <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="companyId"
                        class="w-full p-2 border border-gray-300 rounded"
                        placeholder="Enter your company ID"
                        required
                    >
                    <p id="companyIdError" class="text-red-500 text-xs mt-1 hidden"></p>
                </div>
                <div class="flex items-end">
                    <button
                        type="button"
                        id="submitCompanyIdButton"
                        class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 whitespace-nowrap"
                    >
                        Generate Mandate
                        <svg id="companyIdSpinner" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 loading hidden inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: Mandate Details (initially hidden) -->
        <div id="mandateDetailsStep" class="hidden">
            <h3 class="text-lg font-medium mb-3 text-gray-800">Step 2: Mandate Details</h3>

            <!-- Mandate Reference Display -->
            <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Generated Mandate Reference
                </label>
                <div class="flex items-center">
                    <input
                        type="text"
                        id="mandateReference"
                        class="flex-grow p-2 border border-gray-300 rounded bg-gray-100"
                        readonly
                    >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>

        <form id="createMandateForm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        IBAN
                    </label>
                    <input
                            type="text"
                            name="iban"
                            id="iban"
                            class="w-full p-2 border border-gray-300 rounded"
                            placeholder="**********************"
                    >
                    <p id="ibanError" class="text-red-500 text-xs mt-1 hidden"></p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Signature Date
                    </label>
                    <input
                            type="date"
                            name="signatureDate"
                            id="signatureDate"
                            max=""
                            class="w-full p-2 border border-gray-300 rounded"
                    >
                    <p id="signatureDateError" class="text-red-500 text-xs mt-1 hidden"></p>
                </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Account Holder Name
                        </label>
                        <input
                                type="text"
                                name="accountHolderName"
                                id="accountHolderName"
                                class="w-full p-2 border border-gray-300 rounded"
                                placeholder="Max Mustermann"
                        >
                        <p id="accountHolderNameError" class="text-red-500 text-xs mt-1 hidden"></p>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-4">
                    <button
                            type="button"
                            onclick="resetForm()"
                            class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                    >
                        Cancel
                    </button>
                    <button
                            type="submit"
                            id="createButton"
                            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    >
                        Create Mandate
                        <svg id="createSpinner" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 loading hidden inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>
    </div>

    <!-- Search Mandate -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
            Find Mandate by Reference
        </h2>

        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-grow">
                <input
                        type="text"
                        id="searchReference"
                        class="w-full p-2 border border-gray-300 rounded"
                        placeholder="Enter mandate reference"
                >
            </div>
            <button
                    id="searchButton"
                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 whitespace-nowrap"
            >
                Search
                <svg id="searchSpinner" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 loading hidden inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 6v6l4 2"></path>
                </svg>
            </button>
        </div>

        <!-- Search error message -->
        <div id="searchErrorMessage" class="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded hidden flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <span id="searchErrorMessageText"></span>
        </div>

        <div id="searchResults" class="mt-4 border rounded p-4 hidden">
            <h3 class="font-medium mb-2">Search Results:</h3>
            <div class="grid grid-cols-2 gap-y-2">
                <div class="text-gray-600">Mandate Reference:</div>
                <div id="resultMandateReference"></div>

                <div class="text-gray-600">Company ID:</div>
                <div id="resultCompanyId"></div>

                <div class="text-gray-600">IBAN:</div>
                <div id="resultIban"></div>

                <div class="text-gray-600">Account Holder:</div>
                <div id="resultAccountHolder"></div>

                <div class="text-gray-600">Signature Date:</div>
                <div id="resultSignatureDate"></div>

                <div class="text-gray-600">Status:</div>
                <div id="resultStatus"></div>

                <div class="text-gray-600">Action:</div>
                <div id="resultAction"></div>
            </div>
        </div>
    </div>

    <!-- All Mandates -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
                All Mandates
            </h2>
            <button id="refreshButton" class="text-blue-600 hover:text-blue-800 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" id="refreshIcon" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                Refresh
            </button>
        </div>

        <div id="loadingMandates" class="text-center py-8 hidden">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mx-auto text-blue-600 loading" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
            </svg>
            <p class="mt-2 text-gray-600">Loading mandates...</p>
        </div>

        <div id="noMandates" class="text-center py-8 hidden">
            <p class="text-gray-500">No mandates found</p>
        </div>

        <div id="mandateTableContainer" class="overflow-x-auto shadow-lg rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                <tr>
                    <th class="px-4 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Reference</th>
                    <th class="px-4 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Company ID</th>
                    <th class="px-4 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Account Holder</th>
                    <th class="px-4 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">IBAN</th>
                    <th class="px-4 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8">Signature Date</th>
                    <th class="px-4 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">Status</th>
                    <th class="px-4 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8">Action</th>
                </tr>
                </thead>
                <tbody id="mandateTableBody" class="bg-white divide-y divide-gray-200">
                <!-- Mandates will be inserted here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    function resetForm() {
        document.getElementById('createMandateForm').reset();
        document.getElementById('companyId').value = "";
        document.getElementById('createMandateContainer').classList.add('hidden');
        document.getElementById('companyIdStep').classList.remove('hidden');
        document.getElementById('mandateDetailsStep').classList.add('hidden');
        document.querySelectorAll('p[id$="Error"]').forEach(el => {
            el.textContent = "";
            el.classList.add('hidden');
        });

        // Reset button text to default
        document.getElementById('createButton').innerHTML = `
            Create Mandate
            <svg id="createSpinner" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 loading hidden inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
            </svg>
        `;

        document.getElementById('searchReference').value = "";
        document.getElementById('searchErrorMessage').classList.add('hidden');
        document.getElementById('searchResults').classList.add('hidden');
    }

    async function initializeDraftWithCompanyId(companyId) {
        try {
            showSpinner('companyIdSpinner');
            disableButton('submitCompanyIdButton');
            hideMessages();

            const response = await fetch(`/api/v1/mandates/initialize?companyId=${encodeURIComponent(companyId)}`, {
                method: 'POST'
            });

            if (response.ok) {
                const mandate = await response.json();
                document.getElementById('mandateReference').value = mandate.mandateReference;

                // Show success message
                if (mandate.status === 'DRAFT' && !mandate.iban) {
                    showSuccessMessage(`New mandate created with reference: ${mandate.mandateReference}`);
                } else {
                    showSuccessMessage(`Existing mandate found with reference: ${mandate.mandateReference}`);
                    // Pre-fill form with existing data if available
                    if (mandate.iban) document.getElementById('iban').value = mandate.iban;
                    if (mandate.accountHolderName) document.getElementById('accountHolderName').value = mandate.accountHolderName;
                    if (mandate.signatureDate) document.getElementById('signatureDate').value = mandate.signatureDate;
                }

                // Reset button text for new mandate creation
                document.getElementById('createButton').innerHTML = `
                    Create Mandate
                    <svg id="createSpinner" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 loading hidden inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 6v6l4 2"></path>
                    </svg>
                `;

                // Show step 2
                document.getElementById('mandateDetailsStep').classList.remove('hidden');

            } else {
                let errorText = 'Failed to create mandate';
                try {
                    const errorData = await response.json();
                    errorText = errorData.message || errorText;
                } catch (e) {
                    // If we can't parse error as JSON, use default message
                }
                showErrorMessage(errorText);
            }
        } catch (error) {
            console.error('Error initializing draft mandate:', error);
            showErrorMessage('Failed to create mandate. Please try again.');
        } finally {
            hideSpinner('companyIdSpinner');
            enableButton('submitCompanyIdButton');
        }
    }

    document.getElementById('showCreateMandateButton').addEventListener('click', function() {
        document.getElementById('searchReference').value = "";
        document.getElementById('searchErrorMessage').classList.add('hidden');
        document.getElementById('searchResults').classList.add('hidden');

        document.getElementById('createMandateContainer').classList.remove('hidden');
        document.getElementById('companyIdStep').classList.remove('hidden');
        document.getElementById('mandateDetailsStep').classList.add('hidden');
    });

    // Company ID submission
    document.getElementById('submitCompanyIdButton').addEventListener('click', async function() {
        const companyId = document.getElementById('companyId').value.trim();

        // Clear previous error
        document.getElementById('companyIdError').classList.add('hidden');

        if (!companyId) {
            document.getElementById('companyIdError').textContent = 'Company ID is required';
            document.getElementById('companyIdError').classList.remove('hidden');
            return;
        }

        await initializeDraftWithCompanyId(companyId);
    });

    // Allow Enter key to submit company ID
    document.getElementById('companyId').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('submitCompanyIdButton').click();
        }
    });

    // Set max date for signature date to today
    document.getElementById('signatureDate').max = new Date().toISOString().split('T')[0];

    // Form submission
    document.getElementById('createMandateForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        hideMessages();

        const reference = document.getElementById('mandateReference').value;
        const companyId = document.getElementById('companyId').value;
        const formData = {
            iban: document.getElementById('iban').value,
            signatureDate: document.getElementById('signatureDate').value,
            accountHolderName: document.getElementById('accountHolderName').value,
            companyId: companyId
        };

        showSpinner('createSpinner');
        disableButton('createButton');

        try {
            const response = await fetch(`/api/v1/mandates/${reference}/activate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                let errorText = 'Failed to create mandate';
                try {
                    const errorData = await response.json();
                    errorText = errorData.message || errorText;
                } catch (e) {
                    // If we can't parse error as JSON, use default message
                }
                showErrorMessage(errorText);
                return;
            }

            const data = await response.json();

            // Show success message
            showSuccessMessage(`Mandate created successfully with reference: ${data.mandateReference}`);

            resetForm();

            fetchAllMandates();

        } catch (error) {
            showErrorMessage(error.message || 'Failed to create mandate');
        } finally {
            hideSpinner('createSpinner');
            enableButton('createButton');
        }
    });

    // Search mandate by reference
    document.getElementById('searchButton').addEventListener('click', async () => {
        const reference = document.getElementById('searchReference').value.trim();

        document.getElementById('searchErrorMessage').classList.add('hidden');
        document.getElementById('searchResults').classList.add('hidden');

        if (!reference) {
            showSearchError('Please enter a mandate reference to search');
            return;
        }

        showSpinner('searchSpinner');
        disableButton('searchButton');

        try {
            const response = await fetch(`/api/v1/mandates/${reference}?includeDrafts=true`);

            if (response.status === 404) {
                showSearchError(`No mandate found with reference: ${reference}`);
                return;
            }

            if (!response.ok) {
                let errorText = 'Failed to search for mandate';
                try {
                    const errorData = await response.json();
                    errorText = errorData.message || errorText;
                } catch (e) {
                    // If we can't parse error as JSON, use default message
                }
                showSearchError(errorText);
                return;
            }

            resetForm();
            const data = await response.json();

            // Display results
            document.getElementById('resultMandateReference').textContent = data.mandateReference;
            document.getElementById('resultCompanyId').textContent = data.companyId || 'N/A';
            document.getElementById('resultIban').textContent = data.iban;
            document.getElementById('resultAccountHolder').textContent = data.accountHolderName;
            document.getElementById('resultSignatureDate').textContent = formatDate(data.signatureDate);

            document.getElementById('resultStatus').textContent = data.status;
            if (data.status === "DRAFT") {
                document.getElementById('resultAction').innerHTML = `<button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 whitespace-nowrap" onclick="finalizeMandate('${data.mandateReference}')">Finalize</button>`;
            } else {
                document.getElementById('resultAction').innerHTML = "<span style='font-size:20px;'>&#127974;</span>";
            }

            document.getElementById('searchResults').classList.remove('hidden');

        } catch (error) {
            showSearchError(error.message || 'Failed to search for mandate');
        } finally {
            hideSpinner('searchSpinner');
            enableButton('searchButton');
        }
    });

    // Fetch all mandates
    async function fetchAllMandates() {
        document.getElementById('loadingMandates').classList.remove('hidden');
        document.getElementById('noMandates').classList.add('hidden');
        document.getElementById('mandateTableContainer').classList.add('hidden');
        document.getElementById('refreshIcon').classList.add('loading');
        disableButton('refreshButton');

        try {
            const response = await fetch('/api/v1/mandates?includeDrafts=true');

            if (!response.ok) {
                throw new Error(`Error ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.length === 0) {
                document.getElementById('noMandates').classList.remove('hidden');
                document.getElementById('mandateTableContainer').classList.add('hidden');
            } else {
                const tableBody = document.getElementById('mandateTableBody');
                tableBody.innerHTML = '';

                data.forEach(mandate => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';

                    row.innerHTML = `
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div id="resultMandateReference-${mandate.mandateReference}" class="text-sm font-medium text-gray-900 break-all">${mandate.mandateReference}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div id="resultCompanyId-${mandate.mandateReference}" class="text-sm text-gray-900 font-medium">${mandate.companyId || "N/A"}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div id="resultAccountHolder-${mandate.mandateReference}" class="text-sm text-gray-900">${mandate.accountHolderName || "N/A"}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div id="resultIban-${mandate.mandateReference}" class="text-sm text-gray-500 font-mono break-all">${mandate.iban || "N/A"}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div id="resultSignatureDate-${mandate.mandateReference}" class="text-sm text-gray-500">${formatDate(mandate.signatureDate)}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div id="resultStatus-${mandate.mandateReference}" class="text-sm">
                                <span class="px-2 py-1 rounded-full text-xs font-medium ${mandate.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                    ${mandate.status}
                                </span>
                            </div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div id="resultAction-${mandate.mandateReference}" class="text-sm">
                                ${mandate.status === "DRAFT"
                                    ? `<button class="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 text-xs font-medium" onclick="finalizeMandate('${mandate.mandateReference}', true)">Finalize</button>`
                                    : `<span class="text-green-600 text-lg">✓</span>`}
                            </div>
                        </td>
                    `;

                    tableBody.appendChild(row);
                });

                document.getElementById('mandateTableContainer').classList.remove('hidden');
            }

        } catch (error) {
            showErrorMessage(error.message || 'Failed to fetch mandates');
        } finally {
            document.getElementById('loadingMandates').classList.add('hidden');
            document.getElementById('refreshIcon').classList.remove('loading');
            enableButton('refreshButton');
        }
    }

    async function finalizeMandate(reference, isFromGrid = false) {
        try {
            // First, fetch the complete mandate data from the database
            const response = await fetch(`/api/v1/mandates/${reference}?includeDrafts=true`);

            if (!response.ok) {
                showErrorMessage('Failed to load mandate data');
                return;
            }

            const mandateData = await response.json();

            // Show the create mandate container
            const container = document.getElementById('createMandateContainer');
            container.classList.remove('hidden');

            // Hide step 1 (company ID) and show step 2 (mandate details) directly
            document.getElementById('companyIdStep').classList.add('hidden');
            document.getElementById('mandateDetailsStep').classList.remove('hidden');

            // Populate all fields with data from database
            document.getElementById('mandateReference').value = mandateData.mandateReference;
            document.getElementById('companyId').value = mandateData.companyId || '';
            document.getElementById('iban').value = mandateData.iban || '';
            document.getElementById('accountHolderName').value = mandateData.accountHolderName || '';

            // Handle signature date formatting
            if (mandateData.signatureDate) {
                const date = new Date(mandateData.signatureDate);
                const year = date.getFullYear();
                const month = ('0' + (date.getMonth() + 1)).slice(-2);
                const day = ('0' + date.getDate()).slice(-2);
                document.getElementById('signatureDate').value = `${year}-${month}-${day}`;
            } else {
                document.getElementById('signatureDate').value = "";
            }

            // Update button text to indicate this is editing
            document.getElementById('createButton').innerHTML = `
                Finalize Mandate
                <svg id="createSpinner" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 loading hidden inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 6v6l4 2"></path>
                </svg>
            `;

            // Clear search results and hide search section
            document.getElementById('searchReference').value = "";
            document.getElementById('searchErrorMessage').classList.add('hidden');
            document.getElementById('searchResults').classList.add('hidden');

            // Show success message indicating the mandate is loaded for editing
            showSuccessMessage(`DRAFT mandate loaded for editing: ${mandateData.mandateReference}`);

            // Scroll to the form
            container.scrollIntoView({ behavior: 'smooth' });

        } catch (error) {
            console.error('Error loading mandate for finalization:', error);
            showErrorMessage('Failed to load mandate data for editing');
        }
    }

    // Refresh button
    document.getElementById('refreshButton').addEventListener('click', fetchAllMandates);

    // Helper functions
    function formatDate(dateString) {
        if (!dateString) {
            return "MM.DD.YYYY";
        }
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    function showSuccessMessage(message) {
        const messageElement = document.getElementById('successMessage');
        const messageTextElement = document.getElementById('successMessageText');
        messageTextElement.textContent = message;
        messageElement.classList.remove('hidden');
        messageElement.classList.add('flex');
    }

    function showErrorMessage(message) {
        const messageElement = document.getElementById('errorMessage');
        const messageTextElement = document.getElementById('errorMessageText');
        messageTextElement.textContent = message;
        messageElement.classList.remove('hidden');
        messageElement.classList.add('flex');
    }

    function showSearchError(message) {
        const messageElement = document.getElementById('searchErrorMessage');
        const messageTextElement = document.getElementById('searchErrorMessageText');
        messageTextElement.textContent = message;
        messageElement.classList.remove('hidden');
        messageElement.classList.add('flex');
    }

    function hideMessages() {
        document.getElementById('successMessage').classList.add('hidden');
        document.getElementById('errorMessage').classList.add('hidden');
    }

    function showSpinner(id) {
        document.getElementById(id).classList.remove('hidden');
    }

    function hideSpinner(id) {
        document.getElementById(id).classList.add('hidden');
    }

    function disableButton(id) {
        document.getElementById(id).disabled = true;
        document.getElementById(id).classList.add('opacity-75');
    }

    function enableButton(id) {
        document.getElementById(id).disabled = false;
        document.getElementById(id).classList.remove('opacity-75');
    }

    fetchAllMandates();
</script>
</body>
</html>