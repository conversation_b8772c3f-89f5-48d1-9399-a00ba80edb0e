-- Add company_id column as nullable first
ALTER TABLE sepa_mandates
  ADD COLUMN company_id VARCHAR(255);

-- Update existing records with a default company_id value
-- You may want to customize this based on your business logic
UPDATE sepa_mandates 
SET company_id = 'MIGRATED_' || mandate_reference
WHERE company_id IS NULL;

-- Now make the column NOT NULL
ALTER TABLE sepa_mandates
  ALTER COLUMN company_id SET NOT NULL;
