package org.payment.mandate.repository;

import org.payment.mandate.entity.MandateStatus;
import org.payment.mandate.entity.SepaMandate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SepaMandateRepository extends JpaRepository<SepaMandate, Long> {
    Optional<SepaMandate> findByMandateReference(String reference);
    Optional<SepaMandate> findByMandateReferenceAndStatus(String reference, MandateStatus status);
    Optional<SepaMandate> findByCompanyId(String companyId);

    List<SepaMandate> findAll();
}