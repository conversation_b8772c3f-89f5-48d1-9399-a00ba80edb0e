package org.payment.mandate.service;

import lombok.RequiredArgsConstructor;
import org.payment.mandate.dto.SepaMandateDto;
import org.payment.mandate.entity.MandateStatus;
import org.payment.mandate.entity.SepaMandate;
import org.payment.mandate.tools.SepaMandateMapper;
import org.payment.mandate.repository.SepaMandateRepository;
import org.payment.mandate.tools.SepaMandateValidator;
import org.payment.mandate.validation.ActiveGroup;
import org.payment.mandate.validation.DraftGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
@RequiredArgsConstructor
public class SepaMandateService {
    private static final Logger logger = LoggerFactory.getLogger(SepaMandateService.class);

    private final SepaMandateRepository repository;
    private final SepaMandateMapper mapper;

    // The mandateReference is generated automatically by Hibernate wiht annotation.
    public SepaMandateDto createDraftMandate(String companyId) {
        SepaMandate draft = SepaMandate.builder()
                .status(MandateStatus.DRAFT)
                .companyId(companyId)
                .build();
        SepaMandate savedDraft = repository.save(draft);
        return mapper.toDto(savedDraft);
    }

    // Update a draft mandate with user-entered data.
    public SepaMandateDto updateDraftMandate(String reference, SepaMandateDto dto) {
        SepaMandate draft = repository.findByMandateReference(reference)
                .orElseThrow(() -> new IllegalArgumentException("Draft not found"));
        if (draft.getStatus() != MandateStatus.DRAFT) {
            throw new IllegalStateException("Mandate is not in DRAFT status.");
        }
        if (dto.getIban() != null && dto.getIban() != "") {
            draft.setIban(dto.getIban());
        }
        if (dto.getSignatureDate() != null) {
            draft.setSignatureDate(dto.getSignatureDate());
        }
        if (dto.getAccountHolderName() != null && dto.getAccountHolderName() != "") {
            draft.setAccountHolderName(dto.getAccountHolderName());
        }

        SepaMandateValidator.validate(draft, DraftGroup.class);

        repository.save(draft);
        return mapper.toDto(draft);
    }

    // Finalize the draft mandate and set it to status ACTIVE .
    // The mandateReference generated by Hibernate is reused.
    public SepaMandateDto finalizeDraftMandate(String reference, SepaMandateDto dto) {
        SepaMandate draft = repository.findByMandateReference(reference)
                .orElseThrow(() -> new IllegalArgumentException("Draft not found"));

        if (draft.getStatus() != MandateStatus.DRAFT) {
            throw new IllegalStateException("Cannot finalize a mandate that isn't in DRAFT status.");
        }

        draft.setIban(dto.getIban());
        draft.setSignatureDate(dto.getSignatureDate());
        draft.setAccountHolderName(dto.getAccountHolderName());

        draft.setStatus(MandateStatus.ACTIVE);

        SepaMandateValidator.validate(draft, ActiveGroup.class);

        SepaMandate saved = repository.save(draft);
        return mapper.toDto(saved);
    }

    public void discardDraft(String reference) {
        SepaMandate draft = repository.findByMandateReference(reference)
                .orElseThrow(() -> new IllegalArgumentException("Draft not found"));
        if (draft.getStatus() != MandateStatus.DRAFT) {
            throw new IllegalStateException("Cannot discard a mandate that isn't in DRAFT status.");
        }
        repository.delete(draft);
    }

    public Optional<SepaMandateDto> getMandateByReference(String reference, boolean includeDrafts) {
        if (includeDrafts) {
            return repository.findByMandateReference(reference)
                .map(mapper::toDto);
        } else {
            return repository.findByMandateReferenceAndStatus(reference, MandateStatus.ACTIVE)
                .map(mapper::toDto);
        }
    }

    public List<SepaMandateDto> getAllMandates(boolean includeDrafts) {
        if (includeDrafts) {
            return repository.findAll().stream()
                    .map(mapper::toDto)
                    .collect(Collectors.toList());
        } else {
            return repository.findAll().stream()
                    .filter(m -> m.getStatus() == MandateStatus.ACTIVE)
                    .map(mapper::toDto)
                    .collect(Collectors.toList());
        }
    }

    public List<SepaMandateDto> getAllMandates() {
        return getAllMandates(false);
    }
}