package org.payment.mandate.service;

import lombok.RequiredArgsConstructor;
import org.payment.mandate.dto.SepaMandateDto;
import org.payment.mandate.entity.MandateStatus;
import org.payment.mandate.entity.SepaMandate;
import org.payment.mandate.tools.SepaMandateMapper;
import org.payment.mandate.repository.SepaMandateRepository;
import org.payment.mandate.tools.SepaMandateValidator;
import org.payment.mandate.validation.ActiveGroup;
import org.payment.mandate.validation.DraftGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
@RequiredArgsConstructor
public class SepaMandateService {
    private static final Logger logger = LoggerFactory.getLogger(SepaMandateService.class);

    private final SepaMandateRepository repository;
    private final SepaMandate<PERSON>apper mapper;

    // The mandateReference is generated automatically by Hibernate wiht annotation.
    public SepaMandateDto createDraftMandate(String companyId) {
        logger.info("Checking if mandate already exists for companyId: {}", companyId);

        // First check if a mandate with this companyId already exists
        Optional<SepaMandate> existingMandate = repository.findByCompanyId(companyId);
        if (existingMandate.isPresent()) {
            logger.info("Found existing mandate for companyId: {}, returning existing mandate with reference: {}",
                       companyId, existingMandate.get().getMandateReference());
            return mapper.toDto(existingMandate.get());
        }

        // If no existing mandate found, create a new draft
        logger.info("No existing mandate found for companyId: {}, creating new draft", companyId);
        SepaMandate draft = SepaMandate.builder()
                .status(MandateStatus.DRAFT)
                .companyId(companyId)
                .build();
        SepaMandate savedDraft = repository.save(draft);
        logger.info("Created new draft mandate with reference: {} for companyId: {}",
                   savedDraft.getMandateReference(), companyId);
        return mapper.toDto(savedDraft);
    }

    // Update a draft mandate with user-entered data.
    public SepaMandateDto updateDraftMandate(String reference, SepaMandateDto dto) {
        SepaMandate draft = repository.findByMandateReference(reference)
                .orElseThrow(() -> new IllegalArgumentException("Draft not found"));
        if (draft.getStatus() != MandateStatus.DRAFT) {
            throw new IllegalStateException("Mandate is not in DRAFT status.");
        }
        if (dto.getIban() != null && dto.getIban() != "") {
            draft.setIban(dto.getIban());
        }
        if (dto.getSignatureDate() != null) {
            draft.setSignatureDate(dto.getSignatureDate());
        }
        if (dto.getAccountHolderName() != null && dto.getAccountHolderName() != "") {
            draft.setAccountHolderName(dto.getAccountHolderName());
        }
        if (dto.getCompanyId() != null && dto.getCompanyId() != "") {
            draft.setCompanyId(dto.getCompanyId());
        }

        SepaMandateValidator.validate(draft, DraftGroup.class);

        repository.save(draft);
        return mapper.toDto(draft);
    }

    // Finalize the draft mandate and set it to status ACTIVE .
    // The mandateReference generated by Hibernate is reused.
    public SepaMandateDto finalizeDraftMandate(String reference, SepaMandateDto dto) {
        logger.info("Starting finalization of mandate with reference: {}", reference);

        SepaMandate draft = repository.findByMandateReference(reference)
                .orElseThrow(() -> new IllegalArgumentException("Draft not found"));

        logger.info("Found draft mandate: reference={}, companyId={}, status={}",
                   draft.getMandateReference(), draft.getCompanyId(), draft.getStatus());

        if (draft.getStatus() != MandateStatus.DRAFT) {
            throw new IllegalStateException("Cannot finalize a mandate that isn't in DRAFT status.");
        }

        // Update fields from DTO
        draft.setIban(dto.getIban());
        draft.setSignatureDate(dto.getSignatureDate());
        draft.setAccountHolderName(dto.getAccountHolderName());

        // Preserve existing companyId if not provided in DTO, or update if provided
        if (dto.getCompanyId() != null && !dto.getCompanyId().trim().isEmpty()) {
            draft.setCompanyId(dto.getCompanyId());
        }
        // Note: companyId should already exist from draft creation, so we don't need to set it if not in DTO

        draft.setStatus(MandateStatus.ACTIVE);

        logger.info("About to validate mandate before finalization: reference={}, companyId={}, iban={}, accountHolder={}",
                   draft.getMandateReference(), draft.getCompanyId(), draft.getIban(), draft.getAccountHolderName());

        try {
            SepaMandateValidator.validate(draft, ActiveGroup.class);
            logger.info("Validation successful for mandate: {}", draft.getMandateReference());
        } catch (Exception e) {
            logger.error("Validation failed for mandate {}: {}", draft.getMandateReference(), e.getMessage());
            throw e;
        }

        SepaMandate saved = repository.save(draft);
        logger.info("Successfully finalized mandate: {}", saved.getMandateReference());
        return mapper.toDto(saved);
    }

    public void discardDraft(String reference) {
        SepaMandate draft = repository.findByMandateReference(reference)
                .orElseThrow(() -> new IllegalArgumentException("Draft not found"));
        if (draft.getStatus() != MandateStatus.DRAFT) {
            throw new IllegalStateException("Cannot discard a mandate that isn't in DRAFT status.");
        }
        repository.delete(draft);
    }

    public Optional<SepaMandateDto> getMandateByReference(String reference, boolean includeDrafts) {
        if (includeDrafts) {
            return repository.findByMandateReference(reference)
                .map(mapper::toDto);
        } else {
            return repository.findByMandateReferenceAndStatus(reference, MandateStatus.ACTIVE)
                .map(mapper::toDto);
        }
    }

    public List<SepaMandateDto> getAllMandates(boolean includeDrafts) {
        if (includeDrafts) {
            return repository.findAll().stream()
                    .map(mapper::toDto)
                    .collect(Collectors.toList());
        } else {
            return repository.findAll().stream()
                    .filter(m -> m.getStatus() == MandateStatus.ACTIVE)
                    .map(mapper::toDto)
                    .collect(Collectors.toList());
        }
    }

    public List<SepaMandateDto> getAllMandates() {
        return getAllMandates(false);
    }
}