package org.payment.mandate.web.config.security;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.payment.mandate.tools.KeycloakProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {

    private final KeycloakProperties keycloakProperties;
    private final JwtTokenUtils jwtTokenUtils;
    private final ClientAuthorizationService clientAuthorizationService;

    @Bean
    @Profile("!local") // For all profiles except 'local'
    public SecurityFilterChain filterChain(final HttpSecurity http) throws Exception {
        return http
                .csrf(AbstractHttpConfigurer::disable)
                .sessionManagement(sm -> sm.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(requests -> requests
                        .requestMatchers(new AntPathRequestMatcher("/actuator/health/**")).permitAll()       // allow unauthenticated health endpoint access
                        .requestMatchers(new AntPathRequestMatcher("/actuator/info")).permitAll() // allow unauthenticated info endpoint access
                        .requestMatchers(new AntPathRequestMatcher("/api/v1/mandates/**")).authenticated()
                        .anyRequest().authenticated())
                .oauth2ResourceServer(oauth2 -> oauth2
                        .authenticationManagerResolver(this::authenticationManagerResolver)
                )
                .build();
    }

    @Bean
    @Profile("local")
    public SecurityFilterChain localFilterChain(final HttpSecurity http) throws Exception {
        return http
                .cors(Customizer.withDefaults())
                .csrf(AbstractHttpConfigurer::disable)
                .sessionManagement(sm -> sm.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(requests -> requests
                        .requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                        .anyRequest().permitAll())
                .oauth2ResourceServer(oauth2 -> oauth2
                        .authenticationManagerResolver(this::authenticationManagerResolver)
                )
                .build();
    }


    @Bean
    @Profile("local")
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowedOrigins(List.of("http://localhost:3000", "https://aa.store.dev.local:9002")); // replace with your frontend origin
        config.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        config.setAllowedHeaders(List.of("*"));
        config.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return source;
    }

    Map<String, JwtAuthenticationProvider> authenticationProviders() {
        return keycloakProperties.getJwtIssuers().stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        issuer -> {
                            JwtDecoder decoder = JwtDecoders.fromIssuerLocation(issuer);
                            JwtAuthenticationProvider provider = new JwtAuthenticationProvider(decoder);
                            provider.setJwtAuthenticationConverter(clientAuthorizationService.getClientIdFilteringConverter());
                            return provider;
                        }
                ));
    }

    AuthenticationManager authenticationManagerResolver(HttpServletRequest request) {
        String token = jwtTokenUtils.extractTokenFromRequest(request);
        String issuer = jwtTokenUtils.extractIssuer(token);

        JwtAuthenticationProvider provider = authenticationProviders().get(issuer);
        if (provider == null) {
            throw new RuntimeException("Unrecognized issuer: " + issuer);
        }

        return new ProviderManager(provider);
    }

}
