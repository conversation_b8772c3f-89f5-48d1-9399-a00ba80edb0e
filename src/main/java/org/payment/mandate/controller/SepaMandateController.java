package org.payment.mandate.controller;

import lombok.extern.slf4j.Slf4j;
import org.payment.mandate.dto.SepaMandateDto;
import org.payment.mandate.service.SepaMandateService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://aa.store.dev.local:9002"})
@RestController
@RequestMapping("/api/v1/mandates")
public class SepaMandateController {
    private final SepaMandateService mandateService;

    public SepaMandateController(SepaMandateService mandateService) {
        this.mandateService = mandateService;
    }

    // Create a draft mandate on user attempt to create a mandate (automatically generating the reference)
    // If a mandate already exists for the companyId, return the existing mandate
    @PostMapping("/initialize")
    public ResponseEntity<SepaMandateDto> createMandate(@RequestParam String companyId) {
        log.info("Processing mandate request for company: {}", companyId);
        SepaMandateDto mandate = mandateService.createDraftMandate(companyId);
        log.info("Returning mandate with reference: {} for company: {}", mandate.getMandateReference(), companyId);
        return ResponseEntity.status(HttpStatus.CREATED).body(mandate);
    }

    // If the provided dto contains all required fields, then finalize the draft mandate.
    // Otherwise, update the draft mandate with additional input.
    // this can be ongoing process, and while in progress the mandate will be in draft status.
    @PostMapping("/{reference}/activate")
    public ResponseEntity<SepaMandateDto> activateMandate(@PathVariable String reference,
                                                          @RequestBody(required = false) SepaMandateDto dto) {
        if (isFinalRequest(dto)) {
            log.info("Finalizing draft mandate with reference: {} for IBAN: {}, account holder: {}",
                    reference, dto.getIban(), dto.getAccountHolderName());
            SepaMandateDto finalized = mandateService.finalizeDraftMandate(reference, dto);
            log.info("Mandate finalized with reference: {}", finalized.getMandateReference());
            return ResponseEntity.ok(finalized);
        } else {
            log.info("Incomplete final data for draft mandate with reference: {}. Updating draft.", reference);
            SepaMandateDto updated = mandateService.updateDraftMandate(reference, dto);
            log.info("Draft mandate updated with reference: {}", updated.getMandateReference());
            return ResponseEntity.ok(updated);
        }
    }

    private boolean isFinalRequest(SepaMandateDto dto) {
        if (dto == null) {
            return false;
        }

        return StringUtils.hasText(dto.getIban()) &&
                dto.getSignatureDate() != null &&
                StringUtils.hasText(dto.getAccountHolderName());
    }

    @GetMapping("/{reference}")
    public ResponseEntity<SepaMandateDto> getMandateByReference(
            @PathVariable String reference,
            @RequestParam(defaultValue = "false") boolean includeDrafts) {
        log.info("Retrieving mandate with reference: {} (includeDrafts={})", reference, includeDrafts);
        Optional<SepaMandateDto> result = mandateService.getMandateByReference(reference, includeDrafts);
        return result.map(m -> {
                    log.info("Found mandate with reference: {}", reference);
                    return ResponseEntity.ok(m);
                })
                .orElseGet(() -> {
                    log.warn("No mandate found with reference: {}", reference);
                    return ResponseEntity.notFound().build();
                });
    }

    @GetMapping
    public ResponseEntity<List<SepaMandateDto>> getAllMandates(
            @RequestParam(defaultValue = "false") boolean includeDrafts) {
        log.info("Retrieving mandates, includeDrafts={}", includeDrafts);
        List<SepaMandateDto> mandates = mandateService.getAllMandates(includeDrafts);
        log.info("Retrieved {} mandates", mandates.size());
        return ResponseEntity.ok(mandates);
    }
}