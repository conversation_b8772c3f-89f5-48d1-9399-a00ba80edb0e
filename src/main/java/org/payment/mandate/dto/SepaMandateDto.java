package org.payment.mandate.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Past;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.payment.mandate.entity.MandateStatus;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class SepaMandateDto {

    @Pattern(regexp = "^(?:|[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30})$",
             message = "Invalid IBAN format")
    private String iban;

    @Past(message = "Signature date must be in the past")
    private LocalDate signatureDate;

    @Pattern(regexp = "^(?:|[a-zA-Z\\s-']{2,50})$",
             message = "Account holder name must be 2-50 characters and contain only letters, spaces, hyphens and apostrophes")
    private String accountHolderName;

    @NotNull(message = "Mandate reference is required")
    @Pattern(regexp = "^[A-Za-z0-9\\+\\?\\/\\-:\\(\\)\\.,'\\s]{1,35}$",
             message = "Mandate reference must be between 1 and 35 characters and may contain letters, digits, spaces, and allowed special characters")
    private String mandateReference;

    @NotNull(message = "Mandate status is required")
    private MandateStatus status;

    @NotNull(message = "Company ID is required")
    private String companyId;
}