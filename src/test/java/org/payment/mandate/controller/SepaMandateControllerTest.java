package org.payment.mandate.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.payment.mandate.dto.SepaMandateDto;
import org.payment.mandate.entity.MandateStatus;
import org.payment.mandate.exception.GlobalExceptionHandler;
import org.payment.mandate.service.SepaMandateService;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class SepaMandateControllerTest {

    private static final String MANDATE_REFERENCE = "REF123";
    private static final String BASE_URL = "/api/v1/mandates";

    @Mock
    private SepaMandateService mandateService;

    @InjectMocks
    private SepaMandateController controller;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private SepaMandateDto validMandateDto;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .standaloneSetup(controller)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();

        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules();

        validMandateDto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference(MANDATE_REFERENCE)
                .companyId("COMPANY123")
                .build();
    }

    @Test
    void initializeMandate_shouldCreateDraftAndReturn201_whenNoExistingMandate() throws Exception {
        String companyId = "COMPANY123";
        SepaMandateDto draftCreated = SepaMandateDto.builder()
                .iban(validMandateDto.getIban())
                .signatureDate(validMandateDto.getSignatureDate())
                .accountHolderName(validMandateDto.getAccountHolderName())
                .mandateReference(validMandateDto.getMandateReference())
                .companyId(companyId)
                .status(MandateStatus.DRAFT)
                .build();
        when(mandateService.createDraftMandate(companyId)).thenReturn(draftCreated);

        mockMvc.perform(post(BASE_URL + "/initialize")
                        .param("companyId", companyId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(result -> verify(mandateService).createDraftMandate(companyId));
    }

    @Test
    void initializeMandate_shouldReturnExistingMandateAndReturn201_whenMandateAlreadyExists() throws Exception {
        String companyId = "COMPANY123";
        SepaMandateDto existingMandate = SepaMandateDto.builder()
                .iban(validMandateDto.getIban())
                .signatureDate(validMandateDto.getSignatureDate())
                .accountHolderName(validMandateDto.getAccountHolderName())
                .mandateReference("EXISTING_REF")
                .companyId(companyId)
                .status(MandateStatus.ACTIVE)
                .build();
        when(mandateService.createDraftMandate(companyId)).thenReturn(existingMandate);

        mockMvc.perform(post(BASE_URL + "/initialize")
                        .param("companyId", companyId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(result -> verify(mandateService).createDraftMandate(companyId));
    }

    @Test
    void initializeMandate_shouldReturnServiceUnavailable_whenDatabaseError() throws Exception {
        String companyId = "COMPANY123";
        when(mandateService.createDraftMandate(companyId))
                .thenThrow(new DataAccessResourceFailureException("DB fail"));

        mockMvc.perform(post(BASE_URL + "/initialize")
                        .param("companyId", companyId))
                .andExpect(status().isServiceUnavailable());
    }

    @Test
    void initializeMandate_shouldReturnInternalServerError_whenUnexpectedError() throws Exception {
        String companyId = "COMPANY123";
        when(mandateService.createDraftMandate(companyId))
                .thenThrow(new RuntimeException("Unexpected"));

        mockMvc.perform(post(BASE_URL + "/initialize")
                        .param("companyId", companyId))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void initializeMandate_shouldReturnBadRequest_whenCompanyIdMissing() throws Exception {
        mockMvc.perform(post(BASE_URL + "/initialize")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void activateMandate_shouldFinalize_whenAllFieldsPresent() throws Exception {
        SepaMandateDto requestDto = SepaMandateDto.builder()
                .iban(validMandateDto.getIban())
                .signatureDate(validMandateDto.getSignatureDate())
                .accountHolderName(validMandateDto.getAccountHolderName())
                .mandateReference(validMandateDto.getMandateReference())
                .companyId(validMandateDto.getCompanyId())
                .build();
        SepaMandateDto responseDto = SepaMandateDto.builder()
                .iban(validMandateDto.getIban())
                .signatureDate(validMandateDto.getSignatureDate())
                .accountHolderName(validMandateDto.getAccountHolderName())
                .mandateReference(validMandateDto.getMandateReference())
                .companyId(validMandateDto.getCompanyId())
                .status(MandateStatus.ACTIVE)
                .build();

        when(mandateService.finalizeDraftMandate(eq(MANDATE_REFERENCE), any()))
                .thenReturn(responseDto);

        mockMvc.perform(post(BASE_URL + "/" + MANDATE_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isOk());

        verify(mandateService).finalizeDraftMandate(eq(MANDATE_REFERENCE), any());
    }

    @Test
    void activateMandate_shouldPartialUpdate_whenFieldsMissing() throws Exception {
        SepaMandateDto partialDto = SepaMandateDto.builder()
                .iban(validMandateDto.getIban())
                .signatureDate(null)
                .accountHolderName(validMandateDto.getAccountHolderName())
                .mandateReference(validMandateDto.getMandateReference())
                .companyId(validMandateDto.getCompanyId())
                .build();
        SepaMandateDto updatedDraft = SepaMandateDto.builder()
                .iban(partialDto.getIban())
                .signatureDate(partialDto.getSignatureDate())
                .accountHolderName(partialDto.getAccountHolderName())
                .mandateReference(partialDto.getMandateReference())
                .companyId(partialDto.getCompanyId())
                .status(MandateStatus.DRAFT)
                .build();

        when(mandateService.updateDraftMandate(eq(MANDATE_REFERENCE), any()))
                .thenReturn(updatedDraft);

        mockMvc.perform(post(BASE_URL + "/" + MANDATE_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(partialDto)))
                .andExpect(status().isOk());

        verify(mandateService).updateDraftMandate(eq(MANDATE_REFERENCE), any());
    }

    @Test
    void activateMandate_shouldReturnBadRequest_whenJsonIsInvalid() throws Exception {
        mockMvc.perform(post(BASE_URL + "/" + MANDATE_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{invalid json"))
                .andExpect(status().isBadRequest());

        verify(mandateService, never()).finalizeDraftMandate(any(), any());
        verify(mandateService, never()).updateDraftMandate(any(), any());
    }

    @Test
    void activateMandate_shouldReturnNotFound_whenDraftDoesNotExist() throws Exception {
        when(mandateService.finalizeDraftMandate(eq(MANDATE_REFERENCE), any()))
                .thenThrow(new IllegalArgumentException("Draft not found"));

        SepaMandateDto requestDto = validMandateDto;

        mockMvc.perform(post(BASE_URL + "/" + MANDATE_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isNotFound());
    }

    @Test
    void activateMandate_shouldReturnConflict_whenNotInDraftStatus() throws Exception {
        when(mandateService.finalizeDraftMandate(eq(MANDATE_REFERENCE), any()))
                .thenThrow(new IllegalStateException("Mandate is not in DRAFT status."));

        SepaMandateDto requestDto = validMandateDto;

        mockMvc.perform(post(BASE_URL + "/" + MANDATE_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isConflict());
    }

    @Test
    void activateMandate_shouldReturnServiceUnavailable_whenDatabaseError() throws Exception {
        when(mandateService.finalizeDraftMandate(eq(MANDATE_REFERENCE), any()))
                .thenThrow(new DataAccessResourceFailureException("DB fail"));

        SepaMandateDto requestDto = validMandateDto;

        mockMvc.perform(post(BASE_URL + "/" + MANDATE_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isServiceUnavailable());
    }

    @Test
    void activateMandate_shouldReturnInternalServerError_whenUnexpectedError() throws Exception {
        when(mandateService.finalizeDraftMandate(eq(MANDATE_REFERENCE), any()))
                .thenThrow(new RuntimeException("Unexpected"));

        SepaMandateDto requestDto = validMandateDto;

        mockMvc.perform(post(BASE_URL + "/" + MANDATE_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void getMandateByReference_shouldMapToGetEndpoint() throws Exception {
        when(mandateService.getMandateByReference(MANDATE_REFERENCE, false))
                .thenReturn(Optional.of(validMandateDto));

        mockMvc.perform(get(BASE_URL + "/{reference}", MANDATE_REFERENCE)
                        .param("includeDrafts", "false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(mandateService).getMandateByReference(MANDATE_REFERENCE, false);
    }

    @Test
    void getMandateByReference_shouldReturnNotFound_whenMandateDoesNotExist() throws Exception {
        when(mandateService.getMandateByReference(MANDATE_REFERENCE, false))
                .thenReturn(Optional.empty());

        mockMvc.perform(get(BASE_URL + "/{reference}", MANDATE_REFERENCE)
                        .param("includeDrafts", "false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());

        verify(mandateService).getMandateByReference(MANDATE_REFERENCE, false);
    }

    @Test
    void getAllMandates_shouldMapToGetEndpoint() throws Exception {
        when(mandateService.getAllMandates(eq(false))).thenReturn(List.of(validMandateDto));

        mockMvc.perform(get(BASE_URL)
                        .param("includeDrafts", "false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(mandateService).getAllMandates(eq(false));
    }

    @Test
    void getAllMandates_shouldReturnOk_whenNoMandatesExist() throws Exception {
        when(mandateService.getAllMandates(eq(false))).thenReturn(List.of());

        mockMvc.perform(get(BASE_URL)
                        .param("includeDrafts", "false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(mandateService).getAllMandates(eq(false));
    }

    @Test
    void getAllMandates_shouldReturnServiceUnavailable_whenDatabaseError() throws Exception {
        when(mandateService.getAllMandates(eq(false)))
                .thenThrow(new DataAccessResourceFailureException("Database connection failed"));

        mockMvc.perform(get(BASE_URL)
                        .param("includeDrafts", "false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isServiceUnavailable());
        verify(mandateService).getAllMandates(eq(false));
    }

    @Test
    void getAllMandates_shouldReturnInternalServerError_whenUnexpectedError() throws Exception {
        when(mandateService.getAllMandates(eq(false)))
                .thenThrow(new RuntimeException("Unexpected error"));

        mockMvc.perform(get(BASE_URL)
                        .param("includeDrafts", "false")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
        verify(mandateService).getAllMandates(eq(false));
    }
}