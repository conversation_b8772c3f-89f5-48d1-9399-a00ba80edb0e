package org.payment.mandate.tools;

import org.junit.jupiter.api.Test;
import org.payment.mandate.dto.SepaMandateDto;
import org.payment.mandate.entity.SepaMandate;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class SepaMandateMapperTest {

    private final SepaMandateMapper mapper = new SepaMandateMapper();
    private static final String VALID_IBAN = "**********************";
    private static final LocalDate VALID_DATE = LocalDate.now();
    private static final String VALID_HOLDER = "Max Mustermann";
    private static final String VALID_REFERENCE = "REF123";
    private static final String VALID_COMPANY_ID = "COMPANY123";

    @Test
    void toEntity_ShouldMapAllFields() {
        // arrange
        SepaMandateDto dto = SepaMandateDto.builder()
                .iban(VALID_IBAN)
                .signatureDate(VALID_DATE)
                .accountHolderName(VALID_HOLDER)
                .mandateReference(VALID_REFERENCE)
                .companyId(VALID_COMPANY_ID)
                .build();

        // test
        SepaMandate entity = mapper.toEntity(dto);

        // assert
        assertNotNull(entity);
        assertEquals(dto.getIban(), entity.getIban());
        assertEquals(dto.getSignatureDate(), entity.getSignatureDate());
        assertEquals(dto.getAccountHolderName(), entity.getAccountHolderName());
        assertEquals(dto.getMandateReference(), entity.getMandateReference());
        assertEquals(dto.getCompanyId(), entity.getCompanyId());
    }

    @Test
    void toDto_ShouldMapAllFields() {
        // arrange
        SepaMandate entity = SepaMandate.builder()
                .iban(VALID_IBAN)
                .signatureDate(VALID_DATE)
                .accountHolderName(VALID_HOLDER)
                .mandateReference(VALID_REFERENCE)
                .companyId(VALID_COMPANY_ID)
                .build();

        // test
        SepaMandateDto dto = mapper.toDto(entity);

        // assert
        assertNotNull(dto);
        assertEquals(entity.getIban(), dto.getIban());
        assertEquals(entity.getSignatureDate(), dto.getSignatureDate());
        assertEquals(entity.getAccountHolderName(), dto.getAccountHolderName());
        assertEquals(entity.getMandateReference(), dto.getMandateReference());
        assertEquals(entity.getCompanyId(), dto.getCompanyId());
    }
}