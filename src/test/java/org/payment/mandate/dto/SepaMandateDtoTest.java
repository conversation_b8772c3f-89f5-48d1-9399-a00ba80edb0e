package org.payment.mandate.dto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.payment.mandate.entity.MandateStatus;

import java.time.LocalDate;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class SepaMandateDtoTest {

    private static Validator validator;

    @BeforeAll
    static void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void validSepaMandateDto_shouldPassValidation() {
        SepaMandateDto validDto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123")
                .companyId("COMPANY123")
                .status(MandateStatus.DRAFT)
                .build();

        Set<ConstraintViolation<SepaMandateDto>> violations = validator.validate(validDto);
        assertTrue(violations.isEmpty());
    }

    @Test
    void futureSignatureDate_shouldFailValidation() {
        SepaMandateDto invalidDto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.now().plusDays(1))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123")
                .companyId("COMPANY123")
                .status(MandateStatus.DRAFT)
                .build();

        Set<ConstraintViolation<SepaMandateDto>> violations = validator.validate(invalidDto);
        assertEquals(1, violations.size(), "Future signature date should cause one violation");
    }

    @Test
    void currentDateSignatureDate_shouldPassValidation() {
        SepaMandateDto invalidDto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.now())
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123")
                .companyId("COMPANY123")
                .status(MandateStatus.DRAFT)
                .build();

        Set<ConstraintViolation<SepaMandateDto>> violations = validator.validate(invalidDto);
        assertEquals(0, violations.size());
    }

    @Test
    void invalidMandateReference_shouldFailValidation() {
        SepaMandateDto invalidDto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE_123") // underscore is invalid
                .companyId("COMPANY123")
                .status(MandateStatus.DRAFT)
                .build();

        Set<ConstraintViolation<SepaMandateDto>> violations = validator.validate(invalidDto);
        assertEquals(1, violations.size(), "Invalid mandate reference should cause one violation");
    }

    @Test
    void invalidAccountHolderName_shouldFailValidation() {
        String[] testValues = {
                "A",                   // too short
                "Max Mustermann 123",  // contains numbers
                "Max@Mustermann",      // contains special characters
                "x".repeat(51)   // too long
        };

        String[] allowedValues = {null, ""};

        for (String allowed : allowedValues) {
            SepaMandateDto dto = SepaMandateDto.builder()
                    .iban("**********************")
                    .signatureDate(LocalDate.of(2020, 1, 15))
                    .accountHolderName(allowed)
                    .mandateReference("MANDATE123")
                    .companyId("COMPANY123")
                    .status(MandateStatus.DRAFT)
                    .build();
            Set<ConstraintViolation<SepaMandateDto>> violations = validator.validate(dto);
            assertThat(violations).isEmpty();
        }

        for (String invalid : testValues) {
            SepaMandateDto dto = SepaMandateDto.builder()
                    .iban("**********************")
                    .signatureDate(LocalDate.of(2020, 1, 15))
                    .accountHolderName(invalid)
                    .mandateReference("MANDATE123")
                    .companyId("COMPANY123")
                    .status(MandateStatus.DRAFT)
                    .build();
            Set<ConstraintViolation<SepaMandateDto>> violations = validator.validate(dto);
            assertThat(violations)
                    .hasSize(1)
                    .extracting(ConstraintViolation::getMessage)
                    .contains("Account holder name must be 2-50 characters and contain only letters, spaces, hyphens and apostrophes");
        }
    }

    @Test
    void multipleViolations_shouldFailValidation() {
        SepaMandateDto invalidDto = SepaMandateDto.builder()
                .iban("INVALID")
                .signatureDate(LocalDate.of(3020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123MANDATE123MANDATE123MANDATE123") // 40 chars
                .companyId("COMPANY123")
                .status(MandateStatus.DRAFT)
                .build();

        Set<ConstraintViolation<SepaMandateDto>> violations = validator.validate(invalidDto);
        assertThat(violations)
                .hasSize(3)
                .extracting(ConstraintViolation::getMessage)
                .contains("Invalid IBAN format")
                .contains("Signature date must be in the past")
                .contains("Mandate reference must be between 1 and 35 characters and may contain letters, digits, spaces, and allowed special characters");
    }

}