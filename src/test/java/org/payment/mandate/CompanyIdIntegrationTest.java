package org.payment.mandate;

import org.junit.jupiter.api.Test;
import org.payment.mandate.dto.SepaMandateDto;
import org.payment.mandate.entity.SepaMandate;
import org.payment.mandate.enums.MandateStatus;
import org.payment.mandate.tools.SepaMandateMapper;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test to verify that companyId field is properly handled
 * across the entire application stack.
 */
public class CompanyIdIntegrationTest {

    private final SepaMandateMapper mapper = new SepaMandateMapper();

    @Test
    void testCompanyIdInDtoToEntityMapping() {
        // Given
        String companyId = "COMPANY123";
        SepaMandateDto dto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123")
                .companyId(companyId)
                .status(MandateStatus.DRAFT)
                .build();

        // When
        SepaMandate entity = mapper.toEntity(dto);

        // Then
        assertNotNull(entity);
        assertEquals(companyId, entity.getCompanyId());
        assertEquals(dto.getIban(), entity.getIban());
        assertEquals(dto.getSignatureDate(), entity.getSignatureDate());
        assertEquals(dto.getAccountHolderName(), entity.getAccountHolderName());
        assertEquals(dto.getMandateReference(), entity.getMandateReference());
    }

    @Test
    void testCompanyIdInEntityToDtoMapping() {
        // Given
        String companyId = "COMPANY456";
        SepaMandate entity = SepaMandate.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123")
                .companyId(companyId)
                .status(MandateStatus.ACTIVE)
                .build();

        // When
        SepaMandateDto dto = mapper.toDto(entity);

        // Then
        assertNotNull(dto);
        assertEquals(companyId, dto.getCompanyId());
        assertEquals(entity.getIban(), dto.getIban());
        assertEquals(entity.getSignatureDate(), dto.getSignatureDate());
        assertEquals(entity.getAccountHolderName(), dto.getAccountHolderName());
        assertEquals(entity.getMandateReference(), dto.getMandateReference());
        assertEquals(entity.getStatus(), dto.getStatus());
    }

    @Test
    void testSepaMandateEntityWithCompanyId() {
        // Given
        String companyId = "COMPANY789";

        // When
        SepaMandate mandate = SepaMandate.builder()
                .companyId(companyId)
                .status(MandateStatus.DRAFT)
                .build();

        // Then
        assertNotNull(mandate);
        assertEquals(companyId, mandate.getCompanyId());
        assertEquals(MandateStatus.DRAFT, mandate.getStatus());
    }

    @Test
    void testSepaMandateDtoWithCompanyId() {
        // Given
        String companyId = "COMPANY999";

        // When
        SepaMandateDto dto = SepaMandateDto.builder()
                .companyId(companyId)
                .status(MandateStatus.DRAFT)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(companyId, dto.getCompanyId());
        assertEquals(MandateStatus.DRAFT, dto.getStatus());
    }

    @Test
    void testCreateMandateWorkflow_newCompanyId() {
        // This test simulates the new workflow where createMandate checks for existing mandates
        // Given - a new company ID that doesn't exist in the system
        String newCompanyId = "NEW_COMPANY_123";

        // When - we create a mandate for this company
        // The service should create a new draft mandate
        SepaMandateDto newMandate = SepaMandateDto.builder()
                .companyId(newCompanyId)
                .status(MandateStatus.DRAFT)
                .mandateReference("AUTO_GENERATED_REF")
                .build();

        // Then - verify the new mandate has the correct company ID and is in DRAFT status
        assertNotNull(newMandate);
        assertEquals(newCompanyId, newMandate.getCompanyId());
        assertEquals(MandateStatus.DRAFT, newMandate.getStatus());
        assertNotNull(newMandate.getMandateReference());
    }

    @Test
    void testCreateMandateWorkflow_existingCompanyId() {
        // This test simulates the new workflow where createMandate returns existing mandate
        // Given - an existing company ID with an existing mandate
        String existingCompanyId = "EXISTING_COMPANY_456";

        // When - we try to create a mandate for this company that already has one
        // The service should return the existing mandate
        SepaMandateDto existingMandate = SepaMandateDto.builder()
                .companyId(existingCompanyId)
                .status(MandateStatus.ACTIVE)
                .mandateReference("EXISTING_REF_789")
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Existing Customer")
                .build();

        // Then - verify the existing mandate is returned with all its data
        assertNotNull(existingMandate);
        assertEquals(existingCompanyId, existingMandate.getCompanyId());
        assertEquals(MandateStatus.ACTIVE, existingMandate.getStatus());
        assertEquals("EXISTING_REF_789", existingMandate.getMandateReference());
        assertEquals("**********************", existingMandate.getIban());
        assertEquals("Existing Customer", existingMandate.getAccountHolderName());
    }
}
