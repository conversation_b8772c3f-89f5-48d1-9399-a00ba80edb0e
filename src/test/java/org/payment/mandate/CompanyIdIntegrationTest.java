package org.payment.mandate;

import org.junit.jupiter.api.Test;
import org.payment.mandate.dto.SepaMandateDto;
import org.payment.mandate.entity.SepaMandate;
import org.payment.mandate.enums.MandateStatus;
import org.payment.mandate.tools.SepaMandateMapper;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test to verify that companyId field is properly handled
 * across the entire application stack.
 */
public class CompanyIdIntegrationTest {

    private final SepaMandateMapper mapper = new SepaMandateMapper();

    @Test
    void testCompanyIdInDtoToEntityMapping() {
        // Given
        String companyId = "COMPANY123";
        SepaMandateDto dto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123")
                .companyId(companyId)
                .status(MandateStatus.DRAFT)
                .build();

        // When
        SepaMandate entity = mapper.toEntity(dto);

        // Then
        assertNotNull(entity);
        assertEquals(companyId, entity.getCompanyId());
        assertEquals(dto.getIban(), entity.getIban());
        assertEquals(dto.getSignatureDate(), entity.getSignatureDate());
        assertEquals(dto.getAccountHolderName(), entity.getAccountHolderName());
        assertEquals(dto.getMandateReference(), entity.getMandateReference());
    }

    @Test
    void testCompanyIdInEntityToDtoMapping() {
        // Given
        String companyId = "COMPANY456";
        SepaMandate entity = SepaMandate.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference("MANDATE123")
                .companyId(companyId)
                .status(MandateStatus.ACTIVE)
                .build();

        // When
        SepaMandateDto dto = mapper.toDto(entity);

        // Then
        assertNotNull(dto);
        assertEquals(companyId, dto.getCompanyId());
        assertEquals(entity.getIban(), dto.getIban());
        assertEquals(entity.getSignatureDate(), dto.getSignatureDate());
        assertEquals(entity.getAccountHolderName(), dto.getAccountHolderName());
        assertEquals(entity.getMandateReference(), dto.getMandateReference());
        assertEquals(entity.getStatus(), dto.getStatus());
    }

    @Test
    void testSepaMandateEntityWithCompanyId() {
        // Given
        String companyId = "COMPANY789";
        
        // When
        SepaMandate mandate = SepaMandate.builder()
                .companyId(companyId)
                .status(MandateStatus.DRAFT)
                .build();

        // Then
        assertNotNull(mandate);
        assertEquals(companyId, mandate.getCompanyId());
        assertEquals(MandateStatus.DRAFT, mandate.getStatus());
    }

    @Test
    void testSepaMandateDtoWithCompanyId() {
        // Given
        String companyId = "COMPANY999";
        
        // When
        SepaMandateDto dto = SepaMandateDto.builder()
                .companyId(companyId)
                .status(MandateStatus.DRAFT)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(companyId, dto.getCompanyId());
        assertEquals(MandateStatus.DRAFT, dto.getStatus());
    }
}
