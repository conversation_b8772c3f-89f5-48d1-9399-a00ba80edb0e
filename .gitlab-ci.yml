stages:
  - prepare
  - build
  - test
  - publish
  - deploy

workflow:
  rules:
    # don't run on tag pushes
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_COMMIT_BRANCH

default:
  image: docker.sastdev.net/sast/ci/java-21-with-docker-builder:latest
  tags:
    - sast-cd

variables:
  ECR_REGISTRY_ID: "836989565586"
  ECR_REGION: eu-central-1
  ECR_IMAGE: sepa-mandate
  BUILD_TAG: "${CI_COMMIT_SHORT_SHA}-${CI_COMMIT_REF_SLUG}-SNAPSHOT"
  BUILD_TAG_MASTER: "${CI_COMMIT_SHORT_SHA}"
  ORG_GRADLE_PROJECT_nexusUsername: "${NEXUS_MVN_REPO_USER}"
  ORG_GRADLE_PROJECT_nexusPassword: "${NEXUS_MVN_REPO_PWD}"
  ORG_GRADLE_PROJECT_nexusUrl: "${NEXUS_MVN_REPO_URL}"


before_script:
  - export GRADLE_USER_HOME=$CI_PROJECT_DIR/.gradle
  - chmod +x gradlew
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY

# This job exists to configure versioning
# On master, the version is <commit-date>-<commit-short-sha> (e.g. 20231107-1343ca7c)
# On any other branch, the version is <commit-date>-<commit-short-sha>-<commit-ref-slug>-SNAPSHOT (e.g. 20231107-7151b790-feature-eb-14497-test-branch-SNAPSHOT)
Buildvariables:
  stage: prepare
  script:
    - export COMMIT_TIME=$(echo ${CI_COMMIT_TIMESTAMP:0:11} | sed 's/[-:T]//g')
    - export ECR_TAG=$([ "$CI_COMMIT_BRANCH" == "master" ] && echo "$COMMIT_TIME-$BUILD_TAG_MASTER" || echo "$COMMIT_TIME-$BUILD_TAG")
    - echo "COMMIT_TIME=$COMMIT_TIME"
    - echo "COMMIT_TIME=$COMMIT_TIME" >> build.env
    - echo "ECR_TAG=$ECR_TAG"
    - echo "ECR_TAG=$ECR_TAG" >> build.env
  artifacts:
    reports:
      dotenv: build.env
  cache: {} # disable cache

Publish Artifacts:
  stage: publish
  image: docker.sastdev.net/sast/ci/java-21-with-docker-builder:latest
  before_script:
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
    - eval $(aws ecr get-login --registry-ids $ECR_REGISTRY_ID --no-include-email --region $ECR_REGION)
    - export ECR_REPO_URL="${ECR_REGISTRY_ID}.dkr.ecr.${ECR_REGION}.amazonaws.com"
    - export APP_IMAGE="${ECR_REPO_URL}/${ECR_IMAGE}:${ECR_TAG}"
  script:
    - ./gradlew jib -Djib.httpTimeout=60000 -Djib.console=plain -Pversion=${ECR_TAG}
  after_script:
    - docker system prune -a --volumes -f
  artifacts:
    when: always
    paths:
      - ecr_*txt
    reports:
      metrics: ecr_metric_report.txt
  needs:
    - Buildvariables
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH # automatically publish on master
      when: on_success
      allow_failure: false
    - if: $CI_COMMIT_REF_NAME != $CI_DEFAULT_BRANCH # on other branches, publishing is optional
      when: manual
      allow_failure: true

.deploy-template:
  stage: deploy
  image: docker.sastdev.net/sast/ci/java-21-with-docker-builder:latest
  variables:
    DOWNSTREAM_PROJECT_ID: 713                # sepa-mandate-config repo
    DOWNSTREAM_BRANCH: master                 # default branch of sepa-mandate-config repo
    KUSTOMIZE_SERVICE: sepa-mandate
    KUSTOMIZE_SERVICE_COMPONENT: sepa-mandate
  script:
    - echo "Trigger deployment of service ${KUSTOMIZE_SERVICE}/${KUSTOMIZE_SERVICE_COMPONENT} with tag ${ECR_TAG} in overlay ${OVERLAY}"
    - >
      curl --request POST
      --form token=$CI_JOB_TOKEN
      --form ref=$DOWNSTREAM_BRANCH
      --form variables[ECR_TAG]=$ECR_TAG
      --form variables[KUSTOMIZE_SERVICE]=$KUSTOMIZE_SERVICE
      --form variables[KUSTOMIZE_SERVICE_COMPONENT]=$KUSTOMIZE_SERVICE_COMPONENT
      --form variables[OVERLAY]=$OVERLAY
      https://gitlab.sastdev.net/api/v4/projects/$DOWNSTREAM_PROJECT_ID/trigger/pipeline
  needs:
    - Buildvariables
    - Publish Artifacts

# Build stage
build:
  stage: build
  script:
    - ./gradlew clean compileJava compileTestJava
  artifacts:
    paths:
      - sepa-mandate/build/classes


# Test stage
test:
  stage: test
  script:
    - ./gradlew test
  artifacts:
    when: always
    reports:
      junit: sepa-mandate/build/test-results/test/TEST-*.xml


Deploy DEV:
  extends: .deploy-template
  variables:
    OVERLAY: dev
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH # autdeploy to dev from master
      when: on_success
      allow_failure: true
    - when: manual # can always be triggered
      allow_failure: true

Deploy DEMO:
  extends: .deploy-template
  variables:
    OVERLAY: demo
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH # can only be triggered from the default branch
      when: manual
      allow_failure: true

Deploy LIVE:
  extends: .deploy-template
  variables:
    OVERLAY: live
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH # can only be triggered from the default branch
      when: manual
      allow_failure: true

