rootProject.name = "sepa-mandate"

include("sepa-mandate")
include("sepa-mandate-api")

pluginManagement {
    repositories {
        mavenLocal()
        maven {
            name = "nexus"
            url = uri("${extra["nexusUrl"]}/repository/maven-all")
            credentials(PasswordCredentials::class)
        }
    }
}

dependencyResolutionManagement {
    repositories {
        mavenLocal()
        maven {
            name = "nexus"
            url = uri("${extra["nexusUrl"]}/repository/maven-all")
            credentials(PasswordCredentials::class)
        }
    }
}
