package com.sast.payment.mandate.web.config.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.time.Instant;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ClientValidatingJwtGrantedAuthoritiesConverterTest {

    @Mock
    private ClientAuthorizationService clientAuthorizationService;

    private ClientValidatingJwtGrantedAuthoritiesConverter converter;

    private static final String TEST_AZP = "test-azp";
    private static final String TEST_ISSUER = "test-issuer";
    private static final String TEST_SUBJECT = "test-subject";

    @BeforeEach
    void setUp() {
        converter = new ClientValidatingJwtGrantedAuthoritiesConverter(clientAuthorizationService);
    }

    @Test
    void convertShouldReturnJwtAuthenticationToken() {
        // given
        Map<String, Object> claims = new HashMap<>();
        claims.put("azp", TEST_AZP);
        claims.put("roles", List.of("user", "admin"));

        Jwt jwt = Jwt.withTokenValue("token")
                .header("alg", "RS256")
                .claim("azp", TEST_AZP)
                .claim("roles", List.of("user", "admin"))
                .issuedAt(Instant.now())
                .expiresAt(Instant.now().plusSeconds(300))
                .issuer(TEST_ISSUER)
                .subject(TEST_SUBJECT)
                .build();

        // test
        JwtAuthenticationToken token = converter.convert(jwt);

        // assert
        assertNotNull(token);
        assertEquals(jwt, token.getToken());

        Collection<GrantedAuthority> authorities = token.getAuthorities();
        assertEquals(2, authorities.size());
        assertTrue(authorities.stream().anyMatch(auth -> auth.getAuthority().equals("ROLE_USER")));
        assertTrue(authorities.stream().anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN")));

        verify(clientAuthorizationService).validateAuthorizedParty(TEST_AZP);
    }

    @Test
    void convertShouldHandleEmptyRoles() {
        // given
        Jwt jwt = Jwt.withTokenValue("token")
                .header("alg", "RS256")
                .claim("azp", TEST_AZP)
                .issuedAt(Instant.now())
                .expiresAt(Instant.now().plusSeconds(300))
                .issuer(TEST_ISSUER)
                .subject(TEST_SUBJECT)
                .build();

        // test
        JwtAuthenticationToken token = converter.convert(jwt);

        // assert
        assertNotNull(token);
        Collection<GrantedAuthority> authorities = token.getAuthorities();
        assertEquals(0, authorities.size());

        verify(clientAuthorizationService).validateAuthorizedParty(TEST_AZP);
    }

    @Test
    void convert_ShouldThrowException_WhenAzpIsAbsent() {
        // given
        Jwt jwt = mock(Jwt.class);
        when(jwt.getClaims()).thenReturn(Map.of("roles", List.of("USER")));

        // test & assert
        Exception exception = assertThrows(RuntimeException.class, () -> converter.convert(jwt));
        assertEquals("azp hasn't been found", exception.getMessage());
    }

}
