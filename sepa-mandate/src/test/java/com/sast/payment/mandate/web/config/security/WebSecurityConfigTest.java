package com.sast.payment.mandate.web.config.security;

import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sast.payment.mandate.tools.KeycloakProperties;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.SecurityFilterChain;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class WebSecurityConfigTest {

    @Mock
    private JwtTokenUtils jwtTokenUtils;

    @Mock
    private ClientAuthorizationService clientAuthorizationService;

    @Mock
    private Converter<Jwt, AbstractAuthenticationToken> mockConverter;

    @Mock
    private HttpServletRequest request;

    private KeycloakProperties keycloakProperties = new KeycloakProperties();
    private WebSecurityConfig webSecurityConfig;

    private static final String TEST_ISSUER_1 = "https://test-issuer-1.com";
    private static final String TEST_ISSUER_2 = "https://test-issuer-2.com";

    private static final String VALID_TOKEN = "valid.jwt.token";
    private static final String INVALID_ISSUER = "unknown-issuer";

    @BeforeEach
    void setUp() {
        keycloakProperties.setJwtIssuers(List.of(TEST_ISSUER_1, TEST_ISSUER_2));
        webSecurityConfig = new WebSecurityConfig(keycloakProperties, jwtTokenUtils, clientAuthorizationService);
    }

    @Test
    void authenticationProviders_ShouldCreateProviderForEachIssuer() {
        // when
        NimbusJwtDecoder.JwkSetUriJwtDecoderBuilder mockBuilder = Mockito.mock(NimbusJwtDecoder.JwkSetUriJwtDecoderBuilder.class);
        NimbusJwtDecoder mockDecoder = Mockito.mock(NimbusJwtDecoder.class);
        Mockito.when(mockBuilder.build()).thenReturn(mockDecoder);
        doReturn(mockConverter).when(clientAuthorizationService).getClientIdFilteringConverter();

        try (MockedStatic<NimbusJwtDecoder> mockedStatic = Mockito.mockStatic(NimbusJwtDecoder.class)) {
            mockedStatic.when(() -> NimbusJwtDecoder.withIssuerLocation(Mockito.anyString()).build())
                    .thenReturn(mockBuilder);

            // test
            Map<String, JwtAuthenticationProvider> providers = webSecurityConfig.authenticationProviders();

            // assert
            assertEquals(2, providers.size());
            assertTrue(providers.keySet().containsAll(Set.of(TEST_ISSUER_1, TEST_ISSUER_2)));

            mockedStatic.verify(() -> NimbusJwtDecoder.withIssuerLocation(TEST_ISSUER_1));
            mockedStatic.verify(() -> NimbusJwtDecoder.withIssuerLocation(TEST_ISSUER_2));
            verify(clientAuthorizationService, times(2)).getClientIdFilteringConverter();
        }
    }

    @Test
    void filterChain_shouldConfigureChainCorrectly() throws Exception {
        // given
        HttpSecurity httpSecurity = mock(HttpSecurity.class);

        // when
        when(httpSecurity.csrf(any())).thenReturn(httpSecurity);
        when(httpSecurity.sessionManagement(any())).thenReturn(httpSecurity);
        when(httpSecurity.authorizeHttpRequests(any())).thenReturn(httpSecurity);
        when(httpSecurity.oauth2ResourceServer(any())).thenReturn(httpSecurity);

        DefaultSecurityFilterChain mockChain = mock(DefaultSecurityFilterChain.class);
        when(httpSecurity.build()).thenReturn(mockChain);

        // test
        SecurityFilterChain result = webSecurityConfig.filterChain(httpSecurity);

        // assert
        assertNotNull(result);
        assertEquals(mockChain, result);

        verify(httpSecurity).oauth2ResourceServer(any());
        verify(httpSecurity).authorizeHttpRequests(any());
        verify(httpSecurity).sessionManagement(any());
        verify(httpSecurity).csrf(any());
        verify(httpSecurity).oauth2ResourceServer(any());
        verify(httpSecurity).build();
    }

    @Test
    void shouldReturnAuthenticationManagerForValidIssuer() {
        when(jwtTokenUtils.extractTokenFromRequest(request)).thenReturn(VALID_TOKEN);
        when(jwtTokenUtils.extractIssuer(VALID_TOKEN)).thenReturn(TEST_ISSUER_1);

        NimbusJwtDecoder.JwkSetUriJwtDecoderBuilder mockBuilder = Mockito.mock(NimbusJwtDecoder.JwkSetUriJwtDecoderBuilder.class);
        NimbusJwtDecoder mockDecoder = Mockito.mock(NimbusJwtDecoder.class);
        Mockito.when(mockBuilder.build()).thenReturn(mockDecoder);
        doReturn(mockConverter).when(clientAuthorizationService).getClientIdFilteringConverter();

        try (MockedStatic<NimbusJwtDecoder> mockedStatic = Mockito.mockStatic(NimbusJwtDecoder.class)) {
            mockedStatic.when(() -> NimbusJwtDecoder.withIssuerLocation(Mockito.anyString()).build())
                    .thenReturn(mockBuilder);

            // test
            AuthenticationManager authManager = webSecurityConfig.authenticationManagerResolver(request);

            // assert
            assertNotNull(authManager);
            assertInstanceOf(ProviderManager.class, authManager);
        }
    }

    @Test
    void shouldThrowExceptionIfIssuerIsUnrecognized() {
        when(jwtTokenUtils.extractTokenFromRequest(request)).thenReturn(VALID_TOKEN);
        when(jwtTokenUtils.extractIssuer(VALID_TOKEN)).thenReturn(INVALID_ISSUER);

        NimbusJwtDecoder.JwkSetUriJwtDecoderBuilder mockBuilder = Mockito.mock(NimbusJwtDecoder.JwkSetUriJwtDecoderBuilder.class);
        NimbusJwtDecoder mockDecoder = Mockito.mock(NimbusJwtDecoder.class);
        Mockito.when(mockBuilder.build()).thenReturn(mockDecoder);
        doReturn(mockConverter).when(clientAuthorizationService).getClientIdFilteringConverter();
        Map<String, JwtAuthenticationProvider> providers;

        try (MockedStatic<NimbusJwtDecoder> mockedStatic = Mockito.mockStatic(NimbusJwtDecoder.class)) {
            mockedStatic.when(() -> NimbusJwtDecoder.withIssuerLocation(Mockito.anyString()).build())
                    .thenReturn(mockBuilder);

            assertThrows(RuntimeException.class, () -> {
                webSecurityConfig.authenticationManagerResolver(request);
            }, "Unrecognized issuer: " + INVALID_ISSUER);
        }

    }
}
