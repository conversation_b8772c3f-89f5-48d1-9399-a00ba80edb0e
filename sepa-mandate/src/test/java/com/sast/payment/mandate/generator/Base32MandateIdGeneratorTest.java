package com.sast.payment.mandate.generator;

import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.generator.EventType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

class Base32MandateIdGeneratorTest {

    private Base32MandateIdGenerator generator;
    private GeneratedBase32MandateId annotation;

    @BeforeEach
    void setUp() {
        annotation = new GeneratedBase32MandateId() {
            @Override
            public Class<GeneratedBase32MandateId> annotationType() {
                return GeneratedBase32MandateId.class;
            }
            @Override
            public String prefix() {
                return "MREF";
            }
        };
        generator = new Base32MandateIdGenerator(annotation);
    }

    @Test
    void constructor_shouldUseAnnotationPrefix() {
        SharedSessionContractImplementor session = mock(SharedSessionContractImplementor.class);
        String code = (String) generator.generate(session, null, null, EventType.INSERT);
        assertTrue(code.startsWith("MREF"), "Generator should use the annotation's prefix internally");
    }

    @Test
    void generate_shouldReturnCodeWithPrefix() {
        SharedSessionContractImplementor session = mock(SharedSessionContractImplementor.class);

        Object result = generator.generate(session, null, null, EventType.INSERT);

        assertTrue(result instanceof String, "Generator should return a String code");
        String code = (String) result;
        assertTrue(code.startsWith("MREF"), "Should start with 'MREF'");
        assertEquals(28, code.length(), "Should be prefix length + 24");
    }

    @Test
    void generate_shouldThrowException_forInvalidPrefix() {
        GeneratedBase32MandateId badAnnotation = new GeneratedBase32MandateId() {
            @Override
            public Class<GeneratedBase32MandateId> annotationType() {
                return GeneratedBase32MandateId.class;
            }
            @Override
            public String prefix() {
                return "badPrefix";
            }
        };
        assertThrows(IllegalArgumentException.class, () -> {
            new Base32MandateIdGenerator(badAnnotation);
        });
    }

    @Test
    void generatedMandateReference_shouldNotBeNull() {
        GeneratedBase32MandateId dummyAnnotation = new GeneratedBase32MandateId() {
            @Override
            public Class<GeneratedBase32MandateId> annotationType() {
                return GeneratedBase32MandateId.class;
            }
            @Override
            public String prefix() {
                return "MREF";
            }
        };
        
        Base32MandateIdGenerator generator = new Base32MandateIdGenerator(dummyAnnotation);
        SharedSessionContractImplementor session = mock(SharedSessionContractImplementor.class);
        Object generated = generator.generate(session, null, null, EventType.INSERT);
        assertNotNull(generated, "The generated mandate reference should not be null");
        assertTrue(generated instanceof String);
    }
}
