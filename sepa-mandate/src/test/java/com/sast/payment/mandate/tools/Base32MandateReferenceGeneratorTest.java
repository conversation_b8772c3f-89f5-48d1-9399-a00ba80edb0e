package com.sast.payment.mandate.tools;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class Base32MandateReferenceGeneratorTest {

    @Test
    void isValidPrefix_shouldReturnTrue_forUppercaseAlphanumeric() {
        assertTrue(Base32MandateReferenceGenerator.isValidPrefix("ABC123"));
        assertTrue(Base32MandateReferenceGenerator.isValidPrefix("MREF"));
    }

    @Test
    void isValidPrefix_shouldReturnFalse_forNullOrEmptyOrInvalid() {
        assertFalse(Base32MandateReferenceGenerator.isValidPrefix(null));
        assertFalse(Base32MandateReferenceGenerator.isValidPrefix(""));
        assertFalse(Base32MandateReferenceGenerator.isValidPrefix("abc123")); // not uppercase
        assertFalse(Base32MandateReferenceGenerator.isValidPrefix("ABC-123")); // invalid character '-'
    }

    @Test
    void generateCode_shouldThrowException_forInvalidPrefix() {
        assertThrows(IllegalArgumentException.class, () ->
                Base32MandateReferenceGenerator.generateCode("abc")); // not uppercase
    }

    @Test
    void generateCode_shouldReturnCorrectFormat_forValidPrefix() {
        String code = Base32MandateReferenceGenerator.generateCode("MREF");
        // We expect at least 24 chars of base32, plus the prefix length.
        // prefix "MREF" => length 4 + 24 => total 28
        assertNotNull(code);
        assertTrue(code.startsWith("MREF"));
        assertEquals(28, code.length(), "Should be prefix length + 24");
    }

    @Test
    void generateCode_shouldBeUnique_forMultipleCalls() {
        String code1 = Base32MandateReferenceGenerator.generateCode("MREF");
        String code2 = Base32MandateReferenceGenerator.generateCode("MREF");
        assertNotEquals(code1, code2, "Each generated code should be unique");
    }
}
