package com.sast.payment.mandate.web.config.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sast.payment.mandate.tools.KeycloakProperties;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ClientAuthorizationServiceTest {

    @Mock
    private KeycloakProperties keycloakProperties;

    private static final String CLIENT_1 = "client1";
    private static final String CLIENT_2 = "client2";
    private ClientAuthorizationService clientAuthorizationService;

    @BeforeEach
    void setUp() {
        clientAuthorizationService = new ClientAuthorizationService(keycloakProperties);
    }

    @Test
    void validateClientShouldPassForAuthorizedAuthorizedParty() {
        when(keycloakProperties.getAuthorizedClients()).thenReturn(List.of(CLIENT_1, CLIENT_2));
        assertDoesNotThrow(() -> clientAuthorizationService.validateAuthorizedParty(CLIENT_1));
    }

    @Test
    void validateClientShouldThrowExceptionForUnauthorizedAuthorizedParty() {
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> clientAuthorizationService.validateAuthorizedParty("unauthorized-client"));

        assertEquals("Unauthorized party, azp: unauthorized-client", exception.getMessage());
    }

    @Test
    void getClientIdFilteringConverterShouldReturnConverter() {
        var converter = clientAuthorizationService.getClientIdFilteringConverter();

        assertNotNull(converter);
        assertInstanceOf(ClientValidatingJwtGrantedAuthoritiesConverter.class, converter);
    }
}
