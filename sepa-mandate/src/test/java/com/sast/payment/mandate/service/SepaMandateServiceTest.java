package com.sast.payment.mandate.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sast.payment.mandate.api.SepaMandateDto;
import com.sast.payment.mandate.api.MandateStatus;
import com.sast.payment.mandate.entity.SepaMandate;
import com.sast.payment.mandate.repository.SepaMandateRepository;
import com.sast.payment.mandate.tools.SepaMandateMapper;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SepaMandateServiceTest {

    private static final String REFERENCE = "REF123";

    @Mock
    private SepaMandateRepository repository;

    @Spy
    private SepaMandateMapper mapper = new SepaMandateMapper();

    @InjectMocks
    private SepaMandateService service;

    private SepaMandateDto validMandateDto;
    private SepaMandate validMandateEntity;

    @BeforeEach
    void setUp() {
        // Create a valid DTO for updating or finalizing a draft.
        validMandateDto = SepaMandateDto.builder()
                .iban("**********************")
                .signatureDate(LocalDate.of(2020, 1, 15))
                .accountHolderName("Max Mustermann")
                .mandateReference(REFERENCE)
                .companyId("COMPANY123")
                .status(MandateStatus.DRAFT)
                .build();

        // Create a corresponding entity that resembles a persisted draft with no values initially.
        validMandateEntity = SepaMandate.builder()
                .iban(null)
                .signatureDate(null)
                .accountHolderName(null)
                .mandateReference(REFERENCE)
                .companyId("COMPANY123")
                .status(MandateStatus.DRAFT)
                .build();
    }

    @Test
    void createDraftMandate_shouldReturnDraftWithStatusDraft_whenNoExistingMandate() {
        // Arrange: When a new draft is created, the repository returns the persisted entity.
        String companyId = "COMPANY123";
        SepaMandate draftEntity = validMandateEntity.toBuilder().build();

        when(repository.findByCompanyId(companyId)).thenReturn(Optional.empty());
        when(repository.save(any(SepaMandate.class))).thenReturn(draftEntity);

        // Act: Call createDraftMandate() (no DTO is provided because the identifier is auto-generated).
        SepaMandateDto result = service.createDraftMandate(companyId);

        // Assert: Check that the result is not null and has status DRAFT.
        assertNotNull(result);
        assertEquals(MandateStatus.DRAFT, result.getStatus());
        assertEquals(companyId, result.getCompanyId());
        verify(repository).findByCompanyId(companyId);
        verify(repository).save(any(SepaMandate.class));
    }

    @Test
    void createDraftMandate_shouldReturnExistingMandate_whenMandateAlreadyExists() {
        // Arrange
        String companyId = "COMPANY123";
        SepaMandate existingEntity = validMandateEntity.toBuilder().build();

        when(repository.findByCompanyId(companyId)).thenReturn(Optional.of(existingEntity));

        // Act
        SepaMandateDto result = service.createDraftMandate(companyId);

        // Assert
        assertNotNull(result);
        assertEquals(companyId, result.getCompanyId());
        verify(repository).findByCompanyId(companyId);
        verify(repository, never()).save(any(SepaMandate.class));
    }

    @Test
    void updateDraftMandate_shouldUpdateDraftFields() {
        // Arrange: existing draft with null fields
        when(repository.findByMandateReference(REFERENCE)).thenReturn(Optional.of(validMandateEntity));

        SepaMandateDto updateDto = validMandateDto;  // Using validMandateDto as update data.
        SepaMandate updatedEntity = validMandateEntity.toBuilder()
                .iban(updateDto.getIban())
                .signatureDate(updateDto.getSignatureDate())
                .accountHolderName(updateDto.getAccountHolderName())
                .build();
        SepaMandateDto expectedDto = SepaMandateDto.builder()
                .iban(updateDto.getIban())
                .signatureDate(updateDto.getSignatureDate())
                .accountHolderName(updateDto.getAccountHolderName())
                .mandateReference(updateDto.getMandateReference())
                .companyId(updateDto.getCompanyId())
                .status(MandateStatus.DRAFT)
                .build();

        when(repository.save(any(SepaMandate.class))).thenReturn(updatedEntity);
        doReturn(expectedDto).when(mapper).toDto(any(SepaMandate.class));

        // Act
        SepaMandateDto result = service.updateDraftMandate(REFERENCE, updateDto);

        // Assert
        assertNotNull(result);
        assertEquals(expectedDto, result);
        verify(repository).findByMandateReference(REFERENCE);
        verify(repository).save(any(SepaMandate.class));
    }

    @Test
    void finalizeDraftMandate_shouldActivateDraft_whenAllFieldsPresent() {
        // Arrange: existing draft with no initial values.
        SepaMandate draftEntity = validMandateEntity.toBuilder()
                .iban(null)
                .signatureDate(null)
                .accountHolderName(null)
                .build();
        when(repository.findByMandateReference(REFERENCE)).thenReturn(Optional.of(draftEntity));

        // Prepare the DTO with all required fields.
        SepaMandateDto updateDto = validMandateDto;
        // After finalization, status becomes ACTIVE.
        SepaMandate activatedEntity = draftEntity.toBuilder()
                .iban(updateDto.getIban())
                .signatureDate(updateDto.getSignatureDate())
                .accountHolderName(updateDto.getAccountHolderName())
                .status(MandateStatus.ACTIVE)
                .build();
        SepaMandateDto expectedDto = SepaMandateDto.builder()
                .iban(updateDto.getIban())
                .signatureDate(updateDto.getSignatureDate())
                .accountHolderName(updateDto.getAccountHolderName())
                .mandateReference(updateDto.getMandateReference())
                .companyId(updateDto.getCompanyId())
                .status(MandateStatus.ACTIVE)
                .build();

        when(repository.save(any(SepaMandate.class))).thenReturn(activatedEntity);
        when(mapper.toDto(activatedEntity)).thenReturn(expectedDto);

        // Act
        SepaMandateDto result = service.finalizeDraftMandate(REFERENCE, updateDto);

        // Assert
        assertNotNull(result);
        assertEquals(MandateStatus.ACTIVE, result.getStatus());
        verify(repository).findByMandateReference(REFERENCE);
        verify(repository).save(any(SepaMandate.class));
    }

    @Test
    void discardDraft_shouldDeleteDraft_whenDraftExists() {
        // Arrange
        when(repository.findByMandateReference(REFERENCE)).thenReturn(Optional.of(validMandateEntity));

        // Act
        service.discardDraft(REFERENCE);

        // Assert
        verify(repository).delete(validMandateEntity);
    }

    @Test
    void getMandateByReference_shouldReturnActiveMandate_whenFound() {
        SepaMandate activeEntity = validMandateEntity.toBuilder()
                .status(MandateStatus.ACTIVE)
                .build();
        when(repository.findByMandateReferenceAndStatus(REFERENCE, MandateStatus.ACTIVE))
                .thenReturn(Optional.of(activeEntity));
        when(mapper.toDto(activeEntity)).thenReturn(validMandateDto.toBuilder().status(MandateStatus.ACTIVE).build());

        Optional<SepaMandateDto> result = service.getMandateByReference(REFERENCE, false);

        assertTrue(result.isPresent());
        assertEquals(MandateStatus.ACTIVE, result.get().getStatus());
        verify(repository).findByMandateReferenceAndStatus(REFERENCE, MandateStatus.ACTIVE);
    }

    @Test
    void getMandateByReference_shouldReturnEmpty_whenNotFound() {
        when(repository.findByMandateReferenceAndStatus("INVALID_REF", MandateStatus.ACTIVE))
                .thenReturn(Optional.empty());

        Optional<SepaMandateDto> result = service.getMandateByReference("INVALID_REF", false);

        assertFalse(result.isPresent());
        verify(repository).findByMandateReferenceAndStatus("INVALID_REF", MandateStatus.ACTIVE);
        verify(mapper, never()).toDto(any());
    }

    @Test
    void getAllMandates_shouldReturnAllActiveMandates() {
        SepaMandate activeEntity = validMandateEntity.toBuilder()
                .status(MandateStatus.ACTIVE)
                .build();
        List<SepaMandate> entities = List.of(activeEntity);
        List<SepaMandateDto> expectedDtos = List.of(
                SepaMandateDto.builder()
                        .iban(validMandateDto.getIban())
                        .signatureDate(validMandateDto.getSignatureDate())
                        .accountHolderName(validMandateDto.getAccountHolderName())
                        .mandateReference(validMandateDto.getMandateReference())
                        .companyId(validMandateDto.getCompanyId())
                        .status(MandateStatus.ACTIVE)
                        .build()
        );

        when(repository.findAll()).thenReturn(entities);
        when(mapper.toDto(activeEntity)).thenReturn(expectedDtos.get(0));

        List<SepaMandateDto> result = service.getAllMandates(false);

        assertThat(result).hasSize(1).containsExactlyInAnyOrderElementsOf(expectedDtos);
        verify(repository).findAll();
    }
}
