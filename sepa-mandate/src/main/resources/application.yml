spring:
  application:
    name: sepa-mandate

  datasource:
    url: *******************************************
    username: mandate_user
    password: mandate_pass
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

logging:
  level:
    org:
      springframework:
        jdbc: DEBUG
      hibernate:
        SQL: DEBUG

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: health, info, prometheus
      base-path: /actuator
sepa:
  mandate:
    prefix: "MREF"

keycloak:
    authorized-clients:
      - iotstore-shop
      - bossstore-internal
      - iotstore-sepa-mandate
    jwt-issuers:
        - http://localhost:8080/auth/realms/baam

server:
  port: 8081