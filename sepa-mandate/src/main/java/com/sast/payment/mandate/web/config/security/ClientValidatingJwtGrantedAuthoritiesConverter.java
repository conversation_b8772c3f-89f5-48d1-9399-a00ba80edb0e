package com.sast.payment.mandate.web.config.security;

import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ClientValidatingJwtGrantedAuthoritiesConverter implements Converter<Jwt, JwtAuthenticationToken> {

    private final ClientAuthorizationService clientAuthorizationService;
    private static final String AUTHORIZED_PARTY = "azp";

    @Override
    public JwtAuthenticationToken convert(Jwt jwt) {
        Optional<String> azpOpt = getAuthorizedParty(jwt);
        String azp = azpOpt.orElseThrow(() -> new RuntimeException(AUTHORIZED_PARTY + " hasn't been found"));
        clientAuthorizationService.validateAuthorizedParty(azp);

        Collection<GrantedAuthority> authorities = extractAuthorities(jwt);
        return new JwtAuthenticationToken(jwt, authorities);
    }

    private Optional<String> getAuthorizedParty(Jwt jwt) {
        Map<String, Object> claims = jwt.getClaims();
        return Optional.ofNullable((String) claims.get(AUTHORIZED_PARTY));
    }

    private Collection<GrantedAuthority> extractAuthorities(Jwt jwt) {
        List<String> roles = (List<String>) jwt.getClaims().getOrDefault("roles", List.of());
        return roles.stream().map(role -> (GrantedAuthority) () -> "ROLE_" + role.toUpperCase())
                .collect(Collectors.toList());
    }
}
