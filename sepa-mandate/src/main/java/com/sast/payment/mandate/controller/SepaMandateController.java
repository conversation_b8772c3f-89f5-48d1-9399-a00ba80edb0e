package com.sast.payment.mandate.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.sast.payment.mandate.api.SepaMandateApi;
import com.sast.payment.mandate.api.SepaMandateDto;
import com.sast.payment.mandate.service.SepaMandateService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@CrossOrigin(origins = {"http://localhost:3000", "http://aa.store.dev.local:9002"})
public class SepaMandateController implements SepaMandateApi {
    private final SepaMandateService mandateService;

    @Override
    public ResponseEntity<SepaMandateDto> createMandate(String companyId) {
        log.info("Processing mandate request for company: {}", companyId);
        SepaMandateDto mandate = mandateService.createDraftMandate(companyId);
        log.info("Returning mandate with reference: {} for company: {}", mandate.getMandateReference(), companyId);
        return ResponseEntity.status(HttpStatus.CREATED).body(mandate);
    }

    @Override
    public ResponseEntity<SepaMandateDto> activateMandate(String reference, SepaMandateDto dto) {
        if (isFinalRequest(dto)) {
            log.info("Finalizing draft mandate with reference: {} for IBAN: {}, account holder: {}",
                    reference, dto.getIban(), dto.getAccountHolderName());
            SepaMandateDto finalized = mandateService.finalizeDraftMandate(reference, dto);
            log.info("Mandate finalized with reference: {}", finalized.getMandateReference());
            return ResponseEntity.ok(finalized);
        } else {
            log.info("Incomplete final data for draft mandate with reference: {}. Updating draft.", reference);
            SepaMandateDto updated = mandateService.updateDraftMandate(reference, dto);
            log.info("Draft mandate updated with reference: {}", updated.getMandateReference());
            return ResponseEntity.ok(updated);
        }
    }

    private boolean isFinalRequest(SepaMandateDto dto) {
        if (dto == null) {
            return false;
        }

        return StringUtils.hasText(dto.getIban()) &&
                dto.getSignatureDate() != null &&
                StringUtils.hasText(dto.getAccountHolderName());
    }

    @Override
    public ResponseEntity<SepaMandateDto> getMandateByReference(String reference, boolean includeDrafts) {
        log.info("Retrieving mandate with reference: {} (includeDrafts={})", reference, includeDrafts);
        Optional<SepaMandateDto> result = mandateService.getMandateByReference(reference, includeDrafts);
        return result.map(m -> {
                    log.info("Found mandate with reference: {}", reference);
                    return ResponseEntity.ok(m);
                })
                .orElseGet(() -> {
                    log.warn("No mandate found with reference: {}", reference);
                    return ResponseEntity.notFound().build();
                });
    }

    @Override
    public ResponseEntity<List<SepaMandateDto>> getAllMandates(boolean includeDrafts) {
        log.info("Retrieving mandates, includeDrafts={}", includeDrafts);
        List<SepaMandateDto> mandates = mandateService.getAllMandates(includeDrafts);
        log.info("Retrieved {} mandates", mandates.size());
        return ResponseEntity.ok(mandates);
    }
}
