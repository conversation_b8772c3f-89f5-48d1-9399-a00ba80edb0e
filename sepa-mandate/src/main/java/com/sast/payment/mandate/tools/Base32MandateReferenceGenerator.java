package com.sast.payment.mandate.tools;

import com.google.common.primitives.Bytes;
import com.google.common.primitives.Longs;
import org.apache.commons.codec.binary.Base32;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * Generates a Base32-encoded reference consisting of:
 *   [prefix] + [24-char Base32 code].
 * 
 * The Base32 code is formed by combining:
 *   - truncated timestamp (6 bytes)
 *   - random bytes (9 bytes)
 * 
 * That yields 15 bytes total. Because Base32 has a 5-bit representation,
 * 15 bytes => 120 bits, which divides evenly by 5 => 24 Base32 chars.
 */
public final class Base32MandateReferenceGenerator {

    private static final Pattern VALID_PREFIX_PATTERN = Pattern.compile("^[A-Z0-9]+$");

    private Base32MandateReferenceGenerator() {
        // Prevent instantiation
    }

    /**
     * Validates that the prefix is uppercase alphanumeric.
     * 
     * @param prefix The prefix to validate.
     * @return true if the prefix is uppercase alphanumeric; false otherwise.
     */
    public static boolean isValidPrefix(final String prefix) {
        return prefix != null && VALID_PREFIX_PATTERN.matcher(prefix).matches();
    }

    /**
     * Generates a Base32 reference code with the given prefix.
     * 
     * @param prefix an uppercase alphanumeric prefix (e.g. "MREF" or "ABC123").
     * @return prefix + 24-char Base32 string.
     * @throws IllegalArgumentException if prefix is blank or not uppercase alphanumeric.
     */
    public static String generateCode(final String prefix) {
        if (!isValidPrefix(prefix)) {
            throw new IllegalArgumentException("Invalid prefix: " + prefix);
        }
        return prefix + generateBase32Code();
    }

    /**
     * Internal method that combines truncated timestamp + randomness,
     * then encodes as 24-character Base32 string.
     */
    private static String generateBase32Code() {
        // 1) Convert current time in ms to 8-byte array
        byte[] timeBytes = Longs.toByteArray(Instant.now().toEpochMilli());
        // We'll truncate the first 2 bytes => 6 bytes remain
        byte[] truncatedTime = Arrays.copyOfRange(timeBytes, 2, 8);

        // 2) Generate 9 random bytes
        byte[] randomBytes = new byte[9];
        try {
            SecureRandom.getInstanceStrong().nextBytes(randomBytes);
        } catch (Exception e) {
            new SecureRandom().nextBytes(randomBytes);
        }

        // 3) Combine truncated time + random
        byte[] combined = Bytes.concat(truncatedTime, randomBytes);

        // 4) Encode as Base32 (no padding). This yields exactly 24 Base32 chars.
        return new Base32(true).encodeAsString(combined);
    }
}
