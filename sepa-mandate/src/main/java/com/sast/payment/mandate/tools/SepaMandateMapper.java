package com.sast.payment.mandate.tools;

import com.sast.payment.mandate.api.SepaMandateDto;
import com.sast.payment.mandate.entity.SepaMandate;
import org.springframework.stereotype.Component;

@Component
public class SepaMandateMapper {

    public SepaMandate toEntity(SepaMandateDto dto) {
        return SepaMandate.builder()
                .iban(dto.getIban())
                .signatureDate(dto.getSignatureDate())
                .accountHolderName(dto.getAccountHolderName())
                .mandateReference(dto.getMandateReference())
                .companyId(dto.getCompanyId())
                .build();
    }

    public SepaMandateDto toDto(SepaMandate entity) {
        return SepaMandateDto.builder()
                .iban(entity.getIban())
                .signatureDate(entity.getSignatureDate())
                .accountHolderName(entity.getAccountHolderName())
                .mandateReference(entity.getMandateReference())
                .status(entity.getStatus())
                .companyId(entity.getCompanyId())
                .build();
    }
}
