package com.sast.payment.mandate.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Pattern;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import com.sast.payment.mandate.api.MandateStatus;
import com.sast.payment.mandate.generator.GeneratedBase32MandateId;
import com.sast.payment.mandate.tools.SepaMandateValidator;
import com.sast.payment.mandate.validation.ActiveGroup;
import com.sast.payment.mandate.validation.DraftGroup;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "sepa_mandates")
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
public class SepaMandate {

    @Id
    @GeneratedBase32MandateId
    @Column(name = "mandate_reference", nullable = false, length = 35, unique = true)
    @Pattern(regexp = "^[A-Z0-9\\+\\?\\/\\-:\\(\\)\\.,'\\s]{1,35}$",
            message = "Mandate reference must contain only uppercase letters, digits, spaces, and allowed special characters")
    private String mandateReference;

    @NotNull(message = "IBAN is required", groups = ActiveGroup.class)
    @Pattern(regexp = "^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$",
            message = "Invalid IBAN format", groups = {ActiveGroup.class, DraftGroup.class})
    @Column(nullable = true)
    private String iban;

    @NotNull(message = "Signature date is required", groups = ActiveGroup.class)
    @PastOrPresent(message = "Signature date must be in the past", groups = ActiveGroup.class)
    @Column(name = "signature_date", nullable = true)
    private LocalDate signatureDate;

    @NotNull(message = "Account holder name is required", groups = ActiveGroup.class)
    @Pattern(regexp = "^[a-zA-Z\\s-']{2,50}$",
            message = "Account holder name must be 2-50 characters and contain only letters, spaces, hyphens, and apostrophes",
            groups = {ActiveGroup.class, DraftGroup.class})
    @Column(name = "account_holder_name", nullable = true)
    private String accountHolderName;

    @NotNull(message = "Company ID is required")
    @Column(name = "company_id", nullable = false)
    private String companyId;

    @PrePersist
    @PreUpdate
    public void enforceUpperCase() {
        if (this.mandateReference != null) {
            this.mandateReference = this.mandateReference.toUpperCase();
        }
    }

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private MandateStatus status;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    public static SepaMandate create(String iban, LocalDate signatureDate,
                                     String accountHolderName, String mandateReference, String companyId) {
        SepaMandate mandate = new SepaMandate();
        mandate.toBuilder()
                .iban(iban)
                .signatureDate(signatureDate)
                .accountHolderName(accountHolderName)
                .mandateReference(mandateReference)
                .companyId(companyId)
                .build();

        SepaMandateValidator.validate(mandate);

        return mandate;
    }

}
