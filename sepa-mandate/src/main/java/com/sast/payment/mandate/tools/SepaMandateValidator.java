package com.sast.payment.mandate.tools;

import jakarta.validation.*;

import java.util.Set;
import java.util.stream.Collectors;

public class SepaMandateValidator {

    public static <T> void validate(T mandate) {
        validate(mandate, new Class<?>[0]);
    }

    public static <T> void validate(T mandate, Class<?>... groups) {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<T>> violations = validator.validate(mandate, groups);
        if (!violations.isEmpty()) {
            String errorMessages = violations.stream()
                    .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                    .collect(Collectors.joining(", "));
            throw new ValidationException("Invalid mandate data: " + errorMessages);
        }
    }
}
