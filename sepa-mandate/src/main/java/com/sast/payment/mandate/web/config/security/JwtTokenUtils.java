package com.sast.payment.mandate.web.config.security;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;

import java.util.Base64;

@Component
public class JwtTokenUtils {

    private static final String ISSUER_JSON = "\"iss\":\"";

    public String extractTokenFromRequest(HttpServletRequest request) {
        String authorizationHeader = request.getHeader("Authorization");
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            return authorizationHeader.substring(7);
        }
        throw new RuntimeException("Missing or invalid Authorization header");
    }

    public String extractIssuer(String token) {
        String[] parts = token.split("\\.");
        if (parts.length < 2) {
            throw new RuntimeException("Invalid JWT format");
        }

        String payload = new String(Base64.getDecoder().decode(parts[1]));
        int start = payload.indexOf(ISSUER_JSON) + 7;
        int end = payload.indexOf("\"", start);
        return payload.substring(start, end);
    }
}
