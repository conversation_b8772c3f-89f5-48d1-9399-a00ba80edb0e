package com.sast.payment.mandate.web.config.security;

import lombok.RequiredArgsConstructor;
import com.sast.payment.mandate.tools.KeycloakProperties;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ClientAuthorizationService {

    private final KeycloakProperties keycloakProperties;

    public void validateAuthorizedParty(String azp) {
        if (!keycloakProperties.getAuthorizedClients().contains(azp)) {
            throw new RuntimeException("Unauthorized party, azp: " + azp);
        }
    }

    public Converter<Jwt, ? extends AbstractAuthenticationToken> getClientIdFilteringConverter() {
        return new ClientValidatingJwtGrantedAuthoritiesConverter(this);
    }

}
