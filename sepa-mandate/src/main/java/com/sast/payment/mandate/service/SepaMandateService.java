package com.sast.payment.mandate.service;

import lombok.RequiredArgsConstructor;
import com.sast.payment.mandate.api.SepaMandateDto;
import com.sast.payment.mandate.api.MandateStatus;
import com.sast.payment.mandate.entity.SepaMandate;
import com.sast.payment.mandate.repository.SepaMandateRepository;
import com.sast.payment.mandate.tools.SepaMandateMapper;
import com.sast.payment.mandate.tools.SepaMandateValidator;
import com.sast.payment.mandate.validation.ActiveGroup;
import com.sast.payment.mandate.validation.DraftGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.springframework.util.StringUtils.hasText;

@Service
@Transactional
@RequiredArgsConstructor
public class SepaMandateService {
    private static final Logger logger = LoggerFactory.getLogger(SepaMandateService.class);

    private final SepaMandateRepository repository;
    private final SepaMandateMapper mapper;

    // The mandateReference is generated automatically by Hibernate with annotation.
    public SepaMandateDto createDraftMandate(String companyId) {
        logger.info("Creating draft mandate for companyId: {}", companyId);

        try {
            Optional<SepaMandate> existingMandate = repository.findByCompanyId(companyId);
            if (existingMandate.isPresent()) {
                logger.info("Found existing mandate for companyId: {}, returning existing mandate with reference: {}",
                        companyId, existingMandate.get().getMandateReference());
                return mapper.toDto(existingMandate.get());
            }

            logger.info("No existing mandate found for companyId: {}, creating new draft", companyId);
            SepaMandate draft = SepaMandate.builder()
                    .status(MandateStatus.DRAFT)
                    .companyId(companyId)
                    .build();

            SepaMandate savedDraft = repository.save(draft);
            logger.info("Successfully created draft mandate with reference: {} for companyId: {}",
                    savedDraft.getMandateReference(), companyId);
            return mapper.toDto(savedDraft);

        } catch (Exception e) {
            logger.error("Failed to create draft mandate for companyId: {}", companyId, e);
            throw e;
        }
    }

    // Update a draft mandate with user-entered data.
    public SepaMandateDto updateDraftMandate(String reference, SepaMandateDto dto) {
        logger.info("Updating draft mandate with reference: {}", reference);

        try {
            SepaMandate draft = repository.findByMandateReference(reference)
                    .orElseThrow(() -> new IllegalArgumentException("Draft not found with reference: " + reference));

            if (draft.getStatus() != MandateStatus.DRAFT) {
                throw new IllegalStateException("Mandate is not in DRAFT status. Current status: " + draft.getStatus());
            }

            if (hasText(dto.getIban())) {
                draft.setIban(dto.getIban());
            }
            if (dto.getSignatureDate() != null) {
                draft.setSignatureDate(dto.getSignatureDate());
            }
            if (hasText(dto.getAccountHolderName())) {
                draft.setAccountHolderName(dto.getAccountHolderName());
            }
            if (hasText(dto.getCompanyId())) {
                draft.setCompanyId(dto.getCompanyId());
            }

            SepaMandateValidator.validate(draft, DraftGroup.class);

            SepaMandate savedDraft = repository.save(draft);
            logger.info("Successfully updated draft mandate with reference: {}", reference);
            return mapper.toDto(savedDraft);

        } catch (IllegalArgumentException | IllegalStateException e) {
            logger.error("Business logic error while updating mandate {}: {}", reference, e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Failed to update draft mandate with reference: {}", reference, e);
            throw e;
        }
    }

    // Finalize the draft mandate and set it to status ACTIVE .
    // The mandateReference generated by Hibernate is reused.
    public SepaMandateDto finalizeDraftMandate(String reference, SepaMandateDto dto) {
        logger.info("Starting finalization of mandate with reference: {}", reference);

        try {
            SepaMandate draft = findDraftByReference(reference);
            updateAndActivateMandate(draft, dto);

            SepaMandate saved = repository.save(draft);
            logger.info("Successfully finalized mandate: {}", saved.getMandateReference());
            return mapper.toDto(saved);

        } catch (IllegalArgumentException | IllegalStateException e) {
            logger.error("Business logic error while finalizing mandate {}: {}", reference, e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Failed to finalize mandate with reference: {}", reference, e);
            throw e;
        }
    }

    public void discardDraft(String reference) {
        logger.info("Discarding draft mandate with reference: {}", reference);

        try {
            SepaMandate draft = repository.findByMandateReference(reference)
                    .orElseThrow(() -> new IllegalArgumentException("Draft not found with reference: " + reference));

            if (draft.getStatus() != MandateStatus.DRAFT) {
                throw new IllegalStateException("Cannot discard a mandate that isn't in DRAFT status. Current status: " + draft.getStatus());
            }

            repository.delete(draft);
            logger.info("Successfully discarded draft mandate with reference: {}", reference);

        } catch (IllegalArgumentException | IllegalStateException e) {
            logger.error("Business logic error while discarding mandate {}: {}", reference, e.getMessage());
            throw e; // Re-throw business exceptions as-is
        } catch (Exception e) {
            logger.error("Failed to discard draft mandate with reference: {}", reference, e);
            throw e; // Let global handler deal with database/other exceptions
        }
    }

    public Optional<SepaMandateDto> getMandateByReference(String reference, boolean includeDrafts) {
        logger.debug("Retrieving mandate with reference: {}, includeDrafts: {}", reference, includeDrafts);

        try {
            if (includeDrafts) {
                return repository.findByMandateReference(reference)
                        .map(mapper::toDto);
            } else {
                return repository.findByMandateReferenceAndStatus(reference, MandateStatus.ACTIVE)
                        .map(mapper::toDto);
            }
        } catch (Exception e) {
            logger.error("Failed to retrieve mandate with reference: {}", reference, e);
            throw e;
        }
    }

    public List<SepaMandateDto> getAllMandates(boolean includeDrafts) {
        logger.debug("Retrieving all mandates, includeDrafts: {}", includeDrafts);

        try {
            if (includeDrafts) {
                return repository.findAll().stream()
                        .map(mapper::toDto)
                        .collect(Collectors.toList());
            } else {
                return repository.findAll().stream()
                        .filter(m -> m.getStatus() == MandateStatus.ACTIVE)
                        .map(mapper::toDto)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("Failed to retrieve all mandates", e);
            throw e;
        }
    }

    private SepaMandate findDraftByReference(String reference) {
        SepaMandate draft = repository.findByMandateReference(reference)
                .orElseThrow(() -> new IllegalArgumentException("Draft not found with reference: " + reference));

        logger.info("Found draft mandate: reference={}, companyId={}, status={}",
                draft.getMandateReference(), draft.getCompanyId(), draft.getStatus());

        if (draft.getStatus() != MandateStatus.DRAFT) {
            throw new IllegalStateException("Cannot finalize a mandate that isn't in DRAFT status. Current status: " + draft.getStatus());
        }

        return draft;
    }

    private void updateAndActivateMandate(SepaMandate draft, SepaMandateDto dto) {
        // Update fields from DTO
        draft.setIban(dto.getIban());
        draft.setSignatureDate(dto.getSignatureDate());
        draft.setAccountHolderName(dto.getAccountHolderName());

        // Preserve existing companyId if not provided in DTO, or update if provided
        if (StringUtils.hasText(dto.getCompanyId())) {
            draft.setCompanyId(dto.getCompanyId());
        }

        draft.setStatus(MandateStatus.ACTIVE);

        logger.info("Validating mandate before finalization: reference={}, companyId={}, iban={}, accountHolder={}",
                draft.getMandateReference(), draft.getCompanyId(), draft.getIban(), draft.getAccountHolderName());

        try {
            SepaMandateValidator.validate(draft, ActiveGroup.class);
            logger.info("Validation successful for mandate: {}", draft.getMandateReference());
        } catch (Exception e) {
            logger.error("Validation failed for mandate {}: {}", draft.getMandateReference(), e.getMessage());
            throw new IllegalArgumentException("Mandate validation failed: " + e.getMessage(), e);
        }
    }
}
