package com.sast.payment.mandate.generator;

import java.lang.annotation.*;

import org.hibernate.annotations.IdGeneratorType;

/**
 * Annotation for marking an ID field to be auto-generated using our custom Base32 mandate reference generator.
 * The generated ID will be in the format [prefix]_[Base32-UUID].
 * You can provide a prefix in this annotation; if you leave it empty, the generator will get the prefix from the configuration (defaulting to "MREF").
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@IdGeneratorType(Base32MandateIdGenerator.class)
public @interface GeneratedBase32MandateId {
    String prefix() default "";
}
