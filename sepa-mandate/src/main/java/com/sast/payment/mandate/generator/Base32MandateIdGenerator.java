package com.sast.payment.mandate.generator;

import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.generator.BeforeExecutionGenerator;
import org.hibernate.generator.EventType;
import org.hibernate.generator.EventTypeSets;
import com.sast.payment.mandate.tools.Base32MandateReferenceGenerator;

import java.util.EnumSet;

/**
 * Generates a Base32 ID for a mandate reference.
 * The prefix is taken from the annotation if provided; otherwise, it is loaded from the system property.
 */
public class Base32MandateIdGenerator implements BeforeExecutionGenerator {

    private final String prefix;

    public Base32MandateIdGenerator(final GeneratedBase32MandateId config) {
        String annotatedPrefix = config.prefix();
        if (annotatedPrefix == null || annotatedPrefix.isEmpty()) {
            annotatedPrefix = System.getProperty("sepa.mandate.prefix", "MREF");
        }
        if (!Base32MandateReferenceGenerator.isValidPrefix(annotatedPrefix)) {
            throw new IllegalArgumentException("Invalid prefix: " + annotatedPrefix);
        }
        this.prefix = annotatedPrefix;
    }

    @Override
    public Object generate(SharedSessionContractImplementor session,
                          Object owner,
                          Object currentValue,
                          EventType eventType) {
        return Base32MandateReferenceGenerator.generateCode(prefix);
    }

    @Override
    public EnumSet<EventType> getEventTypes() {
        return EventTypeSets.INSERT_ONLY;
    }
}
