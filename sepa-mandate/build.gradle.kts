plugins {
    java
    id("org.springframework.boot") version "3.4.2"
    id("org.hibernate.orm") version "6.6.5.Final"
    id("com.google.cloud.tools.jib") version "3.4.4"
    id("io.freefair.lombok") version "8.14"
}


val junitVersion = "5.11.4"
val junitPlatformVersion = "1.11.4"


group = "com.sast.payment"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

dependencies {
    implementation(project(":sepa-mandate-api"))

    implementation(enforcedPlatform(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES))
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-test")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-jersey")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation("org.springframework.security:spring-security-oauth2-jose")

    implementation("org.flywaydb:flyway-core:10.10.0")
    implementation("org.flywaydb:flyway-database-postgresql:10.10.0")

    runtimeOnly("ch.qos.logback.contrib:logback-json-classic:0.1.5")
    runtimeOnly("ch.qos.logback.contrib:logback-jackson:0.1.5")

    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.mockito:mockito-junit-jupiter:5.3.1")

    testImplementation("org.junit.jupiter:junit-jupiter:${junitVersion}")
    testImplementation("org.junit.jupiter:junit-jupiter-api:${junitVersion}")
    testImplementation("org.junit.jupiter:junit-jupiter-engine:${junitVersion}")
    testImplementation("org.junit.platform:junit-platform-launcher:${junitPlatformVersion}")

    // PostgreSQL Driver
    runtimeOnly("org.postgresql:postgresql")

    implementation("com.google.guava:guava:31.1-jre")
    implementation("com.fasterxml.uuid:java-uuid-generator:4.0.1")
    implementation("commons-codec:commons-codec:1.15")

}

jib {
    from.image = "gcr.io/distroless/java21-debian12"
    to.image = "************.dkr.ecr.eu-central-1.amazonaws.com/sepa-mandate:${version}"
}

tasks.withType<Test> {
    useJUnitPlatform()
}
